<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemini API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .chat-container {
            border: 1px solid #ccc;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            max-height: 400px;
            overflow-y: auto;
        }
        .message {
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 8px;
        }
        .user-message {
            background-color: #e1f5fe;
            margin-left: 20%;
        }
        .bot-message {
            background-color: #f5f5f5;
            margin-right: 20%;
        }
        .input-container {
            display: flex;
        }
        #user-input {
            flex-grow: 1;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            margin-right: 10px;
        }
        button {
            padding: 10px 20px;
            background-color: #4caf50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top-color: #4caf50;
            animation: spin 1s ease-in-out infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <h1>Gemini API Test</h1>
    <div class="chat-container" id="chat-container">
        <div class="message bot-message">
            Hello! I'm your medical assistant powered by Gemini. How can I help you today?
        </div>
    </div>
    <div class="input-container">
        <input type="text" id="user-input" placeholder="Type your medical question here..." />
        <button id="send-button">Send</button>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const chatContainer = document.getElementById('chat-container');
            const userInput = document.getElementById('user-input');
            const sendButton = document.getElementById('send-button');

            // Function to add a message to the chat
            function addMessage(text, isUser) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${isUser ? 'user-message' : 'bot-message'}`;
                messageDiv.textContent = text;
                chatContainer.appendChild(messageDiv);
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }

            // Function to add a loading indicator
            function addLoadingIndicator() {
                const loadingDiv = document.createElement('div');
                loadingDiv.className = 'message bot-message';
                loadingDiv.id = 'loading-indicator';
                
                const loadingSpinner = document.createElement('div');
                loadingSpinner.className = 'loading';
                loadingDiv.appendChild(loadingSpinner);
                
                chatContainer.appendChild(loadingDiv);
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }

            // Function to remove the loading indicator
            function removeLoadingIndicator() {
                const loadingIndicator = document.getElementById('loading-indicator');
                if (loadingIndicator) {
                    loadingIndicator.remove();
                }
            }

            // Function to send a message to the Gemini API
            async function sendMessage(message) {
                try {
                    addLoadingIndicator();
                    
                    const response = await fetch('http://localhost:3001/api/medical-chat', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            message: message
                        }),
                    });

                    if (!response.ok) {
                        throw new Error(`Server responded with ${response.status}`);
                    }

                    const data = await response.json();
                    removeLoadingIndicator();
                    addMessage(data.response, false);
                } catch (error) {
                    console.error('Error sending message:', error);
                    removeLoadingIndicator();
                    addMessage("I'm sorry, but I'm having trouble connecting to my knowledge base right now. Please try again later.", false);
                }
            }

            // Event listener for the send button
            sendButton.addEventListener('click', function() {
                const message = userInput.value.trim();
                if (message) {
                    addMessage(message, true);
                    userInput.value = '';
                    sendMessage(message);
                }
            });

            // Event listener for the Enter key
            userInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    const message = userInput.value.trim();
                    if (message) {
                        addMessage(message, true);
                        userInput.value = '';
                        sendMessage(message);
                    }
                }
            });
        });
    </script>
</body>
</html>
