rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Allow read/write access to all users for development
    // WARNING: This is insecure and should be changed for production
    match /{allPaths=**} {
      allow read, write: if true;
    }
    
    // For production, use more restrictive rules like:
    // match /avatars/{userId}/{fileName} {
    //   allow read: if request.auth != null;
    //   allow write: if request.auth != null && request.auth.uid == userId;
    // }
    
    // match /social/{type}/{userId}/{fileName} {
    //   allow read: if request.auth != null;
    //   allow write: if request.auth != null && request.auth.uid == userId;
    // }
  }
}
