<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CareAI Logo Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        canvas {
            border: 1px solid #ccc;
            margin: 10px 0;
        }
        button {
            background-color: #3B82F6;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 0;
        }
        button:hover {
            background-color: #2563EB;
        }
    </style>
</head>
<body>
    <h1>CareAI Logo Generator</h1>
    <p>This tool generates logo PNG files for the CareAI app.</p>
    
    <div>
        <h2>Logo 192x192</h2>
        <canvas id="canvas-192" width="192" height="192"></canvas>
        <button onclick="downloadLogo(192)">Download logo192.png</button>
    </div>
    
    <div>
        <h2>Logo 512x512</h2>
        <canvas id="canvas-512" width="512" height="512"></canvas>
        <button onclick="downloadLogo(512)">Download logo512.png</button>
    </div>
    
    <script>
        // Generate logos on page load
        window.onload = function() {
            generateLogo(192);
            generateLogo(512);
        };
        
        // Generate a logo
        function generateLogo(size) {
            const canvas = document.getElementById(`canvas-${size}`);
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.clearRect(0, 0, size, size);
            
            // Background
            ctx.fillStyle = '#3B82F6';
            ctx.beginPath();
            ctx.roundRect(0, 0, size, size, size * 0.25);
            ctx.fill();
            
            // Text
            ctx.fillStyle = 'white';
            ctx.font = `bold ${size * 0.3}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('CareAI', size / 2, size / 2);
            
            // Health symbol
            const symbolSize = size * 0.15;
            ctx.lineWidth = size * 0.05;
            ctx.beginPath();
            ctx.moveTo(size / 2 - symbolSize, size / 2 + symbolSize * 2);
            ctx.lineTo(size / 2 - symbolSize, size / 2 + symbolSize);
            ctx.lineTo(size / 2 - symbolSize * 2, size / 2 + symbolSize);
            ctx.lineTo(size / 2 - symbolSize * 2, size / 2);
            ctx.lineTo(size / 2 - symbolSize, size / 2);
            ctx.lineTo(size / 2 - symbolSize, size / 2 - symbolSize);
            ctx.lineTo(size / 2, size / 2 - symbolSize);
            ctx.lineTo(size / 2, size / 2 - symbolSize * 2);
            ctx.lineTo(size / 2 + symbolSize, size / 2 - symbolSize * 2);
            ctx.lineTo(size / 2 + symbolSize, size / 2 - symbolSize);
            ctx.lineTo(size / 2 + symbolSize * 2, size / 2 - symbolSize);
            ctx.lineTo(size / 2 + symbolSize * 2, size / 2);
            ctx.lineTo(size / 2 + symbolSize, size / 2);
            ctx.lineTo(size / 2 + symbolSize, size / 2 + symbolSize);
            ctx.lineTo(size / 2, size / 2 + symbolSize);
            ctx.lineTo(size / 2, size / 2 + symbolSize * 2);
            ctx.closePath();
            ctx.stroke();
        }
        
        // Download a logo
        function downloadLogo(size) {
            const canvas = document.getElementById(`canvas-${size}`);
            const link = document.createElement('a');
            link.download = `logo${size}.png`;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
    </script>
</body>
</html>
