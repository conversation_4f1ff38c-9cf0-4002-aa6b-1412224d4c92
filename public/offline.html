<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CareAI - You're Offline</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      background: linear-gradient(135deg, #f0f4ff 0%, #e6f0ff 100%);
      color: #333;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      margin: 0;
      padding: 20px;
      text-align: center;
    }
    .container {
      max-width: 500px;
      background-color: white;
      border-radius: 12px;
      padding: 30px;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
    }
    h1 {
      color: #3b82f6;
      margin-bottom: 10px;
    }
    p {
      line-height: 1.6;
      margin-bottom: 20px;
      color: #555;
    }
    .icon {
      width: 80px;
      height: 80px;
      margin-bottom: 20px;
    }
    .button {
      background-color: #3b82f6;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 6px;
      font-weight: 600;
      cursor: pointer;
      transition: background-color 0.3s;
      text-decoration: none;
      display: inline-block;
    }
    .button:hover {
      background-color: #2563eb;
    }
    .offline-data {
      margin-top: 30px;
      padding: 15px;
      background-color: #f8fafc;
      border-radius: 8px;
      border-left: 4px solid #3b82f6;
    }
    .offline-data h3 {
      margin-top: 0;
      color: #3b82f6;
    }
    .offline-data ul {
      text-align: left;
      padding-left: 20px;
    }
    .offline-data li {
      margin-bottom: 8px;
    }
  </style>
</head>
<body>
  <div class="container">
    <svg class="icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="#3b82f6">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636a9 9 0 010 12.728m-3.536-3.536a5 5 0 010-7.072m-3.182 3.182a1 1 0 11-1.414-1.414 1 1 0 011.414 1.414z" />
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3l18 18" />
    </svg>
    
    <h1>You're Offline</h1>
    <p>It looks like you've lost your internet connection. Don't worry - CareAI can still work with limited functionality while you're offline.</p>
    
    <button class="button" onclick="tryReconnect()">Try Again</button>
    
    <div class="offline-data">
      <h3>Available Offline</h3>
      <ul>
        <li>View your recent health metrics</li>
        <li>Check your last saved predictions</li>
        <li>Access your medication schedule</li>
        <li>View saved articles and tips</li>
      </ul>
    </div>
  </div>

  <script>
    function tryReconnect() {
      window.location.reload();
    }
    
    // Check if we're back online
    window.addEventListener('online', () => {
      window.location.reload();
    });
  </script>
</body>
</html>
