{"common": {"about": "À propos", "privacy": "Confidentialité", "terms": "Conditions", "help": "Aide", "allRightsReserved": "Tous droits réservés", "error": "<PERSON><PERSON><PERSON>", "success": "Su<PERSON>ès", "ok": "OK", "cancel": "Annuler", "save": "Enregistrer", "saving": "Enregistrement...", "dailyHealthTip": "Conseil Santé du Jour", "healthTipContent": "Restez hydraté ! Boire au moins 8 verres d'eau par jour aide à maintenir votre niveau d'énergie et favorise une bonne santé générale.", "viewMoreTips": "Voir Plus de Conseils", "spiritual": "<PERSON><PERSON>", "genericError": "Une erreur s'est produite. Veuillez réessayer plus tard.", "backToHome": "Retour à l'accueil"}, "error": {"title": "<PERSON><PERSON><PERSON>", "message": "Une erreur s'est produite. Veuillez réessayer plus tard.", "goBack": "Retour", "goHome": "Accueil"}, "social": {"home": "Accueil", "friends": "<PERSON><PERSON>", "messages": "Messages", "notifications": "Notifications", "explore": "Explorer", "saved": "Enregistrés", "settings": "Paramètres", "searchPlaceholder": "Rechercher des personnes, hashtags ou publications...", "user": "Utilisa<PERSON>ur", "posts": "publications", "post": "Publier", "trendingTopics": "Sujets tendance", "seeMoreTopics": "Voir plus de sujets", "suggestedPeople": "Suggestions", "suggestedForYou": "Suggestions pour vous", "follow": "Suivre", "following": "<PERSON><PERSON><PERSON><PERSON>", "seeMorePeople": "Voir plus de personnes", "upcomingEvents": "Événements à venir", "healthWebinar": "Webinaire Santé & Bien-être", "webinarDescription": "Rejoignez-nous pour une discussion en direct sur le maintien de la santé dans un mode de vie occupé.", "peopleGoing": "personnes y vont", "interested": "<PERSON><PERSON><PERSON><PERSON>", "seeAllEvents": "Voir tous les événements", "createPost": "Créer une publication", "whatsOnYourMind": "Quoi de neuf ?", "addImage": "Ajouter une image", "posting": "Publication en cours...", "like": "<PERSON>'aime", "comment": "<PERSON><PERSON><PERSON>", "share": "Partager", "save": "Enregistrer", "public": "Public", "friendsOnly": "<PERSON>is seulement", "onlyMe": "<PERSON><PERSON> uniquement", "addHashtags": "Ajouter des hashtags...", "postAnonymously": "Publier anonymement", "imageTooLarge": "L'image est trop grande. La taille maximale est de 5 Mo.", "invalidImageType": "Type de fichier invalide. Veuillez sélectionner une image.", "emptyPost": "La publication ne peut pas être vide. Veuillez ajouter du texte ou une image.", "errorCreatingPost": "Erreur lors de la création de la publication. Veuillez réessayer.", "writeComment": "Écrire un commentaire...", "noComments": "Pas encore de commentaires. Soyez le premier à commenter !", "reply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "writeReply": "Écrire une réponse...", "editPost": "Modifier la publication", "deletePost": "Supprimer la publication", "copyLink": "Copier le lien", "linkCopied": "Lien copié dans le presse-papiers", "postUpdatedSuccess": "Publication mise à jour avec succès", "postDeletedSuccess": "Publication supprimée avec succès", "errorUpdatingPost": "Erreur lors de la mise à jour de la publication. Veuillez réessayer.", "errorDeletingPost": "Erreur lors de la suppression de la publication. Veuillez réessayer.", "confirmDeletePost": "Êtes-vous sûr de vouloir supprimer cette publication ?", "writePost": "Écrivez votre publication...", "noTrendingTopics": "Aucun sujet tendance trouvé", "hashtagAnalytics": "Analyse des hashtags", "noHashtagsFound": "Aucun hashtag trouvé", "relatedHashtags": "Hashtags associés", "noPostsForHashtag": "Aucune publication trouvée pour #{tag}", "beFirstToPostHashtag": "Soyez le premier à publier avec #{tag}", "backToFeed": "Retour au fil d'actualité", "loadingPosts": "Chargement des publications...", "errorLoadingHashtagPosts": "Erreur lors du chargement des publications pour ce hashtag", "hashtagNotFound": "Hashtag non trouvé", "recent": "<PERSON><PERSON><PERSON>", "trending": "Tendance", "top": "Top", "commentAddedSuccess": "Commentaire ajouté avec succès", "noPosts": "Aucune publication à afficher", "postSavedSuccess": "Publication enregistrée avec succès", "postUnsavedSuccess": "Publication retirée des éléments enregistrés", "errorSavingPost": "Erreur lors de l'enregistrement de la publication. Veuillez réessayer.", "errorAddingComment": "Erreur lors de l'ajout du commentaire. Veuillez réessayer.", "errorLoadingComments": "Erreur lors du chargement des commentaires", "tryAgainLater": "Veuillez réessayer plus tard", "errorCreatingReply": "Erreur lors de la création de la réponse", "confirmDeleteComment": "Êtes-vous sûr de vouloir supprimer ce commentaire ?", "errorDeletingComment": "<PERSON><PERSON><PERSON> lors de la suppression du commentaire", "replyTo": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{name}}", "peopleYouMayKnow": "Personnes que vous pourriez connaître", "mutualFriends": "amis en commun", "sendRequest": "Envoyer une demande", "requestSent": "<PERSON><PERSON><PERSON> envoy<PERSON>", "acceptRequest": "Accepter", "declineRequest": "Refuser", "friendRequestSent": "<PERSON><PERSON><PERSON> d'ami envoy<PERSON> avec succès", "friendRequestAccepted": "<PERSON><PERSON><PERSON> d'ami acceptée", "friendRequestDeclined": "<PERSON><PERSON><PERSON> d'ami refusée", "errorSendingFriendRequest": "Erreur lors de l'envoi de la demande d'ami", "errorAcceptingFriendRequest": "Erreur lors de l'acceptation de la demande d'ami", "errorDecliningFriendRequest": "Erreur lors du refus de la demande d'ami"}, "nutrition": {"dashboard": "Tableau de bord", "meals": "Repas", "hydration": "Hydratation", "plan": "Plan"}, "pwa": {"installTitle": "Installer l'application CareAI", "installPrompt": "Installez CareAI sur votre appareil pour une meilleure expérience, même hors ligne.", "installPromptIOS": "Installez CareAI sur votre appareil iOS pour une meilleure expérience.", "installButton": "Installer maintenant", "iosStep1": "Appuyez sur le bouton Partager en bas de l'écran.", "iosStep2": "Faites défiler vers le bas et appuyez sur 'Ajouter à l'écran d'accueil'.", "iosStep3": "Appuyez sur 'Ajouter' dans le coin supérieur droit.", "offlineMessage": "Vous êtes actuellement hors ligne. Certaines fonctionnalités peuvent être limitées.", "updateAvailable": "Une nouvelle version est disponible. Actualisez pour mettre à jour.", "updateButton": "Mettre à jour maintenant"}, "bible": {"holyBible": "La Sainte Bible", "read": "<PERSON><PERSON>", "bookmarks": "Signets", "notes": "Notes", "enterReference": "Entrez une référence (ex: <PERSON> 3:16)", "go": "<PERSON><PERSON>", "exampleReference": "Exemples: <PERSON> 3:16, <PERSON><PERSON><PERSON> 1:1, <PERSON><PERSON><PERSON> 23", "continueReading": "<PERSON><PERSON><PERSON> la lecture", "addBookmark": "Ajouter un signet", "reference": "Référence", "note": "Note", "optionalNote": "Ajoutez une note optionnelle sur ce verset", "save": "Enregistrer", "cancel": "Annuler", "noBookmarks": "Pas encore de signets. Ajoutez votre premier signet pour sauvegarder vos versets préférés.", "openInNewTab": "<PERSON><PERSON><PERSON><PERSON>r dans un nouvel onglet", "progressSaved": "Progression de lecture enregistrée", "errorSavingProgress": "Erreur lors de l'enregistrement de la progression", "bookmarkAdded": "Signet ajouté", "errorAddingBookmark": "<PERSON><PERSON><PERSON> lors de l'ajout du signet", "bookmarkDeleted": "Signet supprimé", "errorDeletingBookmark": "<PERSON><PERSON><PERSON> lors de la suppression du signet", "invalidReference": "Format de référence invalide", "errorFetchingBookmarks": "Erreur lors de la récupération des signets", "errorFetchingProgress": "Erreur lors de la récupération de la progression"}}