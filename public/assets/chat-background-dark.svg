<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="500" height="500" viewBox="0 0 500 500" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="chat-pattern-dark" patternUnits="userSpaceOnUse" width="100" height="100">
      <!-- Dark background -->
      <rect width="100" height="100" fill="#0f1419" />
      
      <!-- Chat bubble shapes -->
      <path d="M10,10 Q15,5 20,10 L30,10 Q35,5 40,10 L40,20 Q45,25 40,30 L30,30 Q25,35 20,30 L10,30 Q5,25 10,20 Z" 
            fill="#1a2430" opacity="0.5" />
      
      <path d="M60,60 Q65,55 70,60 L80,60 Q85,55 90,60 L90,70 Q95,75 90,80 L80,80 Q75,85 70,80 L60,80 Q55,75 60,70 Z" 
            fill="#1a2430" opacity="0.5" />
      
      <path d="M20,70 Q25,65 30,70 L40,70 Q45,65 50,70 L50,80 Q55,85 50,90 L40,90 Q35,95 30,90 L20,90 Q15,85 20,80 Z" 
            fill="#1a2430" opacity="0.5" />
      
      <path d="M70,20 Q75,15 80,20 L90,20 Q95,15 100,20 L100,30 Q105,35 100,40 L90,40 Q85,45 80,40 L70,40 Q65,35 70,30 Z" 
            fill="#1a2430" opacity="0.5" />
      
      <!-- Small dots -->
      <circle cx="15" cy="50" r="2" fill="#1a2430" opacity="0.7" />
      <circle cx="85" cy="10" r="2" fill="#1a2430" opacity="0.7" />
      <circle cx="45" cy="85" r="2" fill="#1a2430" opacity="0.7" />
      <circle cx="75" cy="50" r="2" fill="#1a2430" opacity="0.7" />
      <circle cx="25" cy="15" r="2" fill="#1a2430" opacity="0.7" />
      <circle cx="60" cy="35" r="2" fill="#1a2430" opacity="0.7" />
      <circle cx="35" cy="60" r="2" fill="#1a2430" opacity="0.7" />
      <circle cx="90" cy="90" r="2" fill="#1a2430" opacity="0.7" />
    </pattern>
  </defs>
  
  <!-- Apply the pattern to a rectangle covering the entire SVG -->
  <rect width="500" height="500" fill="url(#chat-pattern-dark)" />
</svg>
