<?xml version="1.0" encoding="UTF-8"?>
<svg width="1000" height="600" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Gradient Definitions -->
    <linearGradient id="blue-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3a86ff" stop-opacity="1"/>
      <stop offset="100%" stop-color="#0072ff" stop-opacity="1"/>
    </linearGradient>
    <linearGradient id="purple-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#8338ec" stop-opacity="1"/>
      <stop offset="100%" stop-color="#5e17eb" stop-opacity="1"/>
    </linearGradient>
    <linearGradient id="green-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#10b981" stop-opacity="1"/>
      <stop offset="100%" stop-color="#059669" stop-opacity="1"/>
    </linearGradient>
    <linearGradient id="orange-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#fb923c" stop-opacity="1"/>
      <stop offset="100%" stop-color="#ea580c" stop-opacity="1"/>
    </linearGradient>
    <linearGradient id="red-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#ef4444" stop-opacity="1"/>
      <stop offset="100%" stop-color="#dc2626" stop-opacity="1"/>
    </linearGradient>
    
    <!-- Shadow Filter -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000000" flood-opacity="0.2"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="1000" height="600" fill="#f8fafc" rx="10" ry="10"/>
  <text x="500" y="40" font-family="Arial" font-size="24" font-weight="bold" text-anchor="middle" fill="#1e293b">Ukuqala Technology Stack</text>
  
  <!-- Frontend Stack -->
  <rect x="50" y="80" width="280" height="230" rx="10" ry="10" fill="#eff6ff" stroke="#bfdbfe" stroke-width="2"/>
  <text x="190" y="110" font-family="Arial" font-size="18" font-weight="bold" text-anchor="middle" fill="#2563eb">Frontend</text>
  
  <rect x="70" y="130" width="240" height="40" rx="5" ry="5" fill="white" stroke="#bfdbfe" stroke-width="1" filter="url(#shadow)"/>
  <text x="190" y="155" font-family="Arial" font-size="14" text-anchor="middle" fill="#334155">React / TypeScript</text>
  
  <rect x="70" y="180" width="240" height="40" rx="5" ry="5" fill="white" stroke="#bfdbfe" stroke-width="1" filter="url(#shadow)"/>
  <text x="190" y="205" font-family="Arial" font-size="14" text-anchor="middle" fill="#334155">Tailwind CSS</text>
  
  <rect x="70" y="230" width="110" height="40" rx="5" ry="5" fill="white" stroke="#bfdbfe" stroke-width="1" filter="url(#shadow)"/>
  <text x="125" y="255" font-family="Arial" font-size="14" text-anchor="middle" fill="#334155">Framer Motion</text>
  
  <rect x="200" y="230" width="110" height="40" rx="5" ry="5" fill="white" stroke="#bfdbfe" stroke-width="1" filter="url(#shadow)"/>
  <text x="255" y="255" font-family="Arial" font-size="14" text-anchor="middle" fill="#334155">React Router</text>
  
  <!-- Backend Stack -->
  <rect x="360" y="80" width="280" height="230" rx="10" ry="10" fill="#f0fdf4" stroke="#bbf7d0" stroke-width="2"/>
  <text x="500" y="110" font-family="Arial" font-size="18" font-weight="bold" text-anchor="middle" fill="#16a34a">Backend</text>
  
  <rect x="380" y="130" width="240" height="40" rx="5" ry="5" fill="url(#blue-gradient)" stroke="#bfdbfe" stroke-width="1" filter="url(#shadow)"/>
  <text x="500" y="155" font-family="Arial" font-size="14" text-anchor="middle" fill="white">Supabase</text>
  
  <rect x="380" y="180" width="240" height="40" rx="5" ry="5" fill="url(#orange-gradient)" stroke="#fed7aa" stroke-width="1" filter="url(#shadow)"/>
  <text x="500" y="205" font-family="Arial" font-size="14" text-anchor="middle" fill="white">Firebase</text>
  
  <rect x="380" y="230" width="110" height="40" rx="5" ry="5" fill="url(#green-gradient)" stroke="#bbf7d0" stroke-width="1" filter="url(#shadow)"/>
  <text x="435" y="255" font-family="Arial" font-size="14" text-anchor="middle" fill="white">FastAPI</text>
  
  <rect x="510" y="230" width="110" height="40" rx="5" ry="5" fill="white" stroke="#bbf7d0" stroke-width="1" filter="url(#shadow)"/>
  <text x="565" y="255" font-family="Arial" font-size="14" text-anchor="middle" fill="#334155">Edge Functions</text>
  
  <!-- Database Stack -->
  <rect x="670" y="80" width="280" height="230" rx="10" ry="10" fill="#fff7ed" stroke="#fed7aa" stroke-width="2"/>
  <text x="810" y="110" font-family="Arial" font-size="18" font-weight="bold" text-anchor="middle" fill="#ea580c">Database</text>
  
  <rect x="690" y="130" width="240" height="40" rx="5" ry="5" fill="url(#blue-gradient)" stroke="#bfdbfe" stroke-width="1" filter="url(#shadow)"/>
  <text x="810" y="155" font-family="Arial" font-size="14" text-anchor="middle" fill="white">PostgreSQL</text>
  
  <rect x="690" y="180" width="240" height="40" rx="5" ry="5" fill="url(#orange-gradient)" stroke="#fed7aa" stroke-width="1" filter="url(#shadow)"/>
  <text x="810" y="205" font-family="Arial" font-size="14" text-anchor="middle" fill="white">Firestore</text>
  
  <rect x="690" y="230" width="110" height="40" rx="5" ry="5" fill="white" stroke="#fed7aa" stroke-width="1" filter="url(#shadow)"/>
  <text x="745" y="255" font-family="Arial" font-size="14" text-anchor="middle" fill="#334155">Firebase Storage</text>
  
  <rect x="820" y="230" width="110" height="40" rx="5" ry="5" fill="white" stroke="#fed7aa" stroke-width="1" filter="url(#shadow)"/>
  <text x="875" y="255" font-family="Arial" font-size="14" text-anchor="middle" fill="#334155">IndexedDB</text>
  
  <!-- AI/ML Stack -->
  <rect x="50" y="340" width="280" height="230" rx="10" ry="10" fill="#f5f3ff" stroke="#e9d5ff" stroke-width="2"/>
  <text x="190" y="370" font-family="Arial" font-size="18" font-weight="bold" text-anchor="middle" fill="#7c3aed">AI/ML</text>
  
  <rect x="70" y="390" width="240" height="40" rx="5" ry="5" fill="url(#purple-gradient)" stroke="#e9d5ff" stroke-width="1" filter="url(#shadow)"/>
  <text x="190" y="415" font-family="Arial" font-size="14" text-anchor="middle" fill="white">Hugging Face</text>
  
  <rect x="70" y="440" width="240" height="40" rx="5" ry="5" fill="white" stroke="#e9d5ff" stroke-width="1" filter="url(#shadow)"/>
  <text x="190" y="465" font-family="Arial" font-size="14" text-anchor="middle" fill="#334155">TensorFlow.js</text>
  
  <rect x="70" y="490" width="110" height="40" rx="5" ry="5" fill="white" stroke="#e9d5ff" stroke-width="1" filter="url(#shadow)"/>
  <text x="125" y="515" font-family="Arial" font-size="14" text-anchor="middle" fill="#334155">OpenBioLLM</text>
  
  <rect x="200" y="490" width="110" height="40" rx="5" ry="5" fill="white" stroke="#e9d5ff" stroke-width="1" filter="url(#shadow)"/>
  <text x="255" y="515" font-family="Arial" font-size="14" text-anchor="middle" fill="#334155">vLLM</text>
  
  <!-- DevOps & Security -->
  <rect x="360" y="340" width="280" height="230" rx="10" ry="10" fill="#fee2e2" stroke="#fecaca" stroke-width="2"/>
  <text x="500" y="370" font-family="Arial" font-size="18" font-weight="bold" text-anchor="middle" fill="#dc2626">DevOps & Security</text>
  
  <rect x="380" y="390" width="240" height="40" rx="5" ry="5" fill="white" stroke="#fecaca" stroke-width="1" filter="url(#shadow)"/>
  <text x="500" y="415" font-family="Arial" font-size="14" text-anchor="middle" fill="#334155">GitHub Actions</text>
  
  <rect x="380" y="440" width="240" height="40" rx="5" ry="5" fill="url(#blue-gradient)" stroke="#bfdbfe" stroke-width="1" filter="url(#shadow)"/>
  <text x="500" y="465" font-family="Arial" font-size="14" text-anchor="middle" fill="white">Row-Level Security</text>
  
  <rect x="380" y="490" width="110" height="40" rx="5" ry="5" fill="white" stroke="#fecaca" stroke-width="1" filter="url(#shadow)"/>
  <text x="435" y="515" font-family="Arial" font-size="14" text-anchor="middle" fill="#334155">JWT Auth</text>
  
  <rect x="510" y="490" width="110" height="40" rx="5" ry="5" fill="white" stroke="#fecaca" stroke-width="1" filter="url(#shadow)"/>
  <text x="565" y="515" font-family="Arial" font-size="14" text-anchor="middle" fill="#334155">PWA</text>
  
  <!-- External APIs -->
  <rect x="670" y="340" width="280" height="230" rx="10" ry="10" fill="#f0f9ff" stroke="#bae6fd" stroke-width="2"/>
  <text x="810" y="370" font-family="Arial" font-size="18" font-weight="bold" text-anchor="middle" fill="#0284c7">External APIs</text>
  
  <rect x="690" y="390" width="240" height="40" rx="5" ry="5" fill="white" stroke="#bae6fd" stroke-width="1" filter="url(#shadow)"/>
  <text x="810" y="415" font-family="Arial" font-size="14" text-anchor="middle" fill="#334155">Weather API</text>
  
  <rect x="690" y="440" width="240" height="40" rx="5" ry="5" fill="white" stroke="#bae6fd" stroke-width="1" filter="url(#shadow)"/>
  <text x="810" y="465" font-family="Arial" font-size="14" text-anchor="middle" fill="#334155">News API</text>
  
  <rect x="690" y="490" width="240" height="40" rx="5" ry="5" fill="white" stroke="#bae6fd" stroke-width="1" filter="url(#shadow)"/>
  <text x="810" y="515" font-family="Arial" font-size="14" text-anchor="middle" fill="#334155">Africa's Talking API</text>
</svg>
