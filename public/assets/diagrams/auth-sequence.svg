<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Gradient Definitions -->
    <linearGradient id="blue-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3a86ff" stop-opacity="1"/>
      <stop offset="100%" stop-color="#0072ff" stop-opacity="1"/>
    </linearGradient>
    
    <!-- Shadow Filter -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000000" flood-opacity="0.2"/>
    </filter>
    
    <!-- <PERSON> Marker -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="0" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#64748b"/>
    </marker>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="600" fill="#f8fafc" rx="10" ry="10"/>
  <text x="400" y="40" font-family="Arial" font-size="24" font-weight="bold" text-anchor="middle" fill="#1e293b">Authentication Sequence Diagram</text>
  
  <!-- Actors -->
  <rect x="100" y="80" width="100" height="50" rx="8" ry="8" fill="white" stroke="#cbd5e1" stroke-width="2" filter="url(#shadow)"/>
  <text x="150" y="110" font-family="Arial" font-size="14" text-anchor="middle" fill="#334155">User</text>
  
  <rect x="250" y="80" width="100" height="50" rx="8" ry="8" fill="url(#blue-gradient)" stroke="#bfdbfe" stroke-width="2" filter="url(#shadow)"/>
  <text x="300" y="110" font-family="Arial" font-size="14" text-anchor="middle" fill="white">Frontend</text>
  
  <rect x="400" y="80" width="100" height="50" rx="8" ry="8" fill="url(#blue-gradient)" stroke="#bfdbfe" stroke-width="2" filter="url(#shadow)"/>
  <text x="450" y="110" font-family="Arial" font-size="14" text-anchor="middle" fill="white">Supabase</text>
  
  <rect x="550" y="80" width="100" height="50" rx="8" ry="8" fill="url(#blue-gradient)" stroke="#bfdbfe" stroke-width="2" filter="url(#shadow)"/>
  <text x="600" y="110" font-family="Arial" font-size="14" text-anchor="middle" fill="white">AuthContext</text>
  
  <!-- Lifelines -->
  <line x1="150" y1="130" x2="150" y2="550" stroke="#cbd5e1" stroke-width="2" stroke-dasharray="5,5"/>
  <line x1="300" y1="130" x2="300" y2="550" stroke="#bfdbfe" stroke-width="2" stroke-dasharray="5,5"/>
  <line x1="450" y1="130" x2="450" y2="550" stroke="#bfdbfe" stroke-width="2" stroke-dasharray="5,5"/>
  <line x1="600" y1="130" x2="600" y2="550" stroke="#bfdbfe" stroke-width="2" stroke-dasharray="5,5"/>
  
  <!-- Step 1: User enters credentials -->
  <rect x="150" y="150" width="150" height="30" fill="#eff6ff" stroke="#bfdbfe" stroke-width="1" rx="5" ry="5"/>
  <polygon points="290,165 280,160 280,170" fill="#bfdbfe"/>
  <text x="225" y="170" font-family="Arial" font-size="12" text-anchor="middle" fill="#334155">Enter Credentials</text>
  
  <!-- Step 2: Frontend validates input -->
  <rect x="300" y="190" width="0" height="30" fill="#eff6ff" stroke="#bfdbfe" stroke-width="1" rx="5" ry="5"/>
  <rect x="250" y="190" width="100" height="30" fill="#eff6ff" stroke="#bfdbfe" stroke-width="1" rx="5" ry="5"/>
  <text x="300" y="210" font-family="Arial" font-size="12" text-anchor="middle" fill="#334155">Validate Input</text>
  
  <!-- Step 3: Frontend sends auth request -->
  <rect x="300" y="230" width="150" height="30" fill="#eff6ff" stroke="#bfdbfe" stroke-width="1" rx="5" ry="5"/>
  <polygon points="440,245 430,240 430,250" fill="#bfdbfe"/>
  <text x="375" y="250" font-family="Arial" font-size="12" text-anchor="middle" fill="#334155">signIn() Request</text>
  
  <!-- Step 4: Supabase authenticates -->
  <rect x="450" y="270" width="0" height="30" fill="#eff6ff" stroke="#bfdbfe" stroke-width="1" rx="5" ry="5"/>
  <rect x="400" y="270" width="100" height="30" fill="#eff6ff" stroke="#bfdbfe" stroke-width="1" rx="5" ry="5"/>
  <text x="450" y="290" font-family="Arial" font-size="12" text-anchor="middle" fill="#334155">Authenticate</text>
  
  <!-- Step 5: Supabase returns token -->
  <rect x="300" y="310" width="150" height="30" fill="#eff6ff" stroke="#bfdbfe" stroke-width="1" rx="5" ry="5"/>
  <polygon points="310,325 320,320 320,330" fill="#bfdbfe"/>
  <text x="375" y="330" font-family="Arial" font-size="12" text-anchor="middle" fill="#334155">Return Session Token</text>
  
  <!-- Step 6: Frontend updates AuthContext -->
  <rect x="300" y="350" width="300" height="30" fill="#eff6ff" stroke="#bfdbfe" stroke-width="1" rx="5" ry="5"/>
  <polygon points="590,365 580,360 580,370" fill="#bfdbfe"/>
  <text x="450" y="370" font-family="Arial" font-size="12" text-anchor="middle" fill="#334155">Update Auth State</text>
  
  <!-- Step 7: PIN verification -->
  <rect x="150" y="390" width="150" height="30" fill="#eff6ff" stroke="#bfdbfe" stroke-width="1" rx="5" ry="5"/>
  <polygon points="290,405 280,400 280,410" fill="#bfdbfe"/>
  <text x="225" y="410" font-family="Arial" font-size="12" text-anchor="middle" fill="#334155">Enter PIN</text>
  
  <rect x="300" y="430" width="0" height="30" fill="#eff6ff" stroke="#bfdbfe" stroke-width="1" rx="5" ry="5"/>
  <rect x="250" y="430" width="100" height="30" fill="#eff6ff" stroke="#bfdbfe" stroke-width="1" rx="5" ry="5"/>
  <text x="300" y="450" font-family="Arial" font-size="12" text-anchor="middle" fill="#334155">Verify PIN</text>
  
  <!-- Step 8: Load user profile -->
  <rect x="300" y="470" width="150" height="30" fill="#eff6ff" stroke="#bfdbfe" stroke-width="1" rx="5" ry="5"/>
  <polygon points="440,485 430,480 430,490" fill="#bfdbfe"/>
  <text x="375" y="490" font-family="Arial" font-size="12" text-anchor="middle" fill="#334155">Load User Profile</text>
  
  <rect x="300" y="510" width="150" height="30" fill="#eff6ff" stroke="#bfdbfe" stroke-width="1" rx="5" ry="5"/>
  <polygon points="310,525 320,520 320,530" fill="#bfdbfe"/>
  <text x="375" y="530" font-family="Arial" font-size="12" text-anchor="middle" fill="#334155">Return Profile Data</text>
  
  <!-- Step 9: Redirect to home -->
  <rect x="150" y="550" width="150" height="30" fill="#eff6ff" stroke="#bfdbfe" stroke-width="1" rx="5" ry="5"/>
  <polygon points="160,565 170,560 170,570" fill="#bfdbfe"/>
  <text x="225" y="570" font-family="Arial" font-size="12" text-anchor="middle" fill="#334155">Redirect to Home</text>
</svg>
