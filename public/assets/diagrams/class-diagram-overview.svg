<?xml version="1.0" encoding="UTF-8"?>
<svg width="1000" height="700" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Gradient Definitions -->
    <linearGradient id="blue-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3a86ff" stop-opacity="1"/>
      <stop offset="100%" stop-color="#0072ff" stop-opacity="1"/>
    </linearGradient>
    <linearGradient id="purple-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#8338ec" stop-opacity="1"/>
      <stop offset="100%" stop-color="#5e17eb" stop-opacity="1"/>
    </linearGradient>
    <linearGradient id="green-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#10b981" stop-opacity="1"/>
      <stop offset="100%" stop-color="#059669" stop-opacity="1"/>
    </linearGradient>
    <linearGradient id="orange-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#fb923c" stop-opacity="1"/>
      <stop offset="100%" stop-color="#ea580c" stop-opacity="1"/>
    </linearGradient>
    
    <!-- Shadow Filter -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000000" flood-opacity="0.2"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="1000" height="700" fill="#f8fafc" rx="10" ry="10"/>
  <text x="500" y="40" font-family="Arial" font-size="24" font-weight="bold" text-anchor="middle" fill="#1e293b">Ukuqala Class Diagram Overview</text>
  
  <!-- Frontend Components -->
  <rect x="50" y="80" width="400" height="280" rx="10" ry="10" fill="#eff6ff" stroke="#bfdbfe" stroke-width="2"/>
  <text x="250" y="110" font-family="Arial" font-size="18" font-weight="bold" text-anchor="middle" fill="#2563eb">Frontend Components</text>
  
  <!-- Page Components -->
  <rect x="80" y="130" width="160" height="200" rx="8" ry="8" fill="white" stroke="#bfdbfe" stroke-width="2" filter="url(#shadow)"/>
  <rect x="80" y="130" width="160" height="30" rx="8" ry="8" fill="url(#blue-gradient)" stroke="#bfdbfe" stroke-width="0"/>
  <text x="160" y="150" font-family="Arial" font-size="14" font-weight="bold" text-anchor="middle" fill="white">Page Components</text>
  
  <rect x="90" y="170" width="140" height="25" rx="4" ry="4" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1"/>
  <text x="100" y="187" font-family="Arial" font-size="12" fill="#334155">Home</text>
  
  <rect x="90" y="200" width="140" height="25" rx="4" ry="4" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1"/>
  <text x="100" y="217" font-family="Arial" font-size="12" fill="#334155">Predictions</text>
  
  <rect x="90" y="230" width="140" height="25" rx="4" ry="4" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1"/>
  <text x="100" y="247" font-family="Arial" font-size="12" fill="#334155">Analytics</text>
  
  <rect x="90" y="260" width="140" height="25" rx="4" ry="4" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1"/>
  <text x="100" y="277" font-family="Arial" font-size="12" fill="#334155">Social</text>
  
  <rect x="90" y="290" width="140" height="25" rx="4" ry="4" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1"/>
  <text x="100" y="307" font-family="Arial" font-size="12" fill="#334155">Profile</text>
  
  <!-- UI Components -->
  <rect x="260" y="130" width="160" height="200" rx="8" ry="8" fill="white" stroke="#bfdbfe" stroke-width="2" filter="url(#shadow)"/>
  <rect x="260" y="130" width="160" height="30" rx="8" ry="8" fill="url(#blue-gradient)" stroke="#bfdbfe" stroke-width="0"/>
  <text x="340" y="150" font-family="Arial" font-size="14" font-weight="bold" text-anchor="middle" fill="white">UI Components</text>
  
  <rect x="270" y="170" width="140" height="25" rx="4" ry="4" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1"/>
  <text x="280" y="187" font-family="Arial" font-size="12" fill="#334155">Layout</text>
  
  <rect x="270" y="200" width="140" height="25" rx="4" ry="4" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1"/>
  <text x="280" y="217" font-family="Arial" font-size="12" fill="#334155">Header</text>
  
  <rect x="270" y="230" width="140" height="25" rx="4" ry="4" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1"/>
  <text x="280" y="247" font-family="Arial" font-size="12" fill="#334155">HealthCard</text>
  
  <rect x="270" y="260" width="140" height="25" rx="4" ry="4" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1"/>
  <text x="280" y="277" font-family="Arial" font-size="12" fill="#334155">Chart</text>
  
  <rect x="270" y="290" width="140" height="25" rx="4" ry="4" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1"/>
  <text x="280" y="307" font-family="Arial" font-size="12" fill="#334155">Modal</text>
  
  <!-- Context Providers -->
  <rect x="550" y="80" width="400" height="280" rx="10" ry="10" fill="#f0fdf4" stroke="#bbf7d0" stroke-width="2"/>
  <text x="750" y="110" font-family="Arial" font-size="18" font-weight="bold" text-anchor="middle" fill="#16a34a">Context Providers</text>
  
  <!-- Auth Contexts -->
  <rect x="580" y="130" width="160" height="200" rx="8" ry="8" fill="white" stroke="#bbf7d0" stroke-width="2" filter="url(#shadow)"/>
  <rect x="580" y="130" width="160" height="30" rx="8" ry="8" fill="url(#green-gradient)" stroke="#bbf7d0" stroke-width="0"/>
  <text x="660" y="150" font-family="Arial" font-size="14" font-weight="bold" text-anchor="middle" fill="white">Auth Contexts</text>
  
  <rect x="590" y="170" width="140" height="25" rx="4" ry="4" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1"/>
  <text x="600" y="187" font-family="Arial" font-size="12" fill="#334155">AuthContext</text>
  
  <rect x="590" y="200" width="140" height="25" rx="4" ry="4" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1"/>
  <text x="600" y="217" font-family="Arial" font-size="12" fill="#334155">UserContext</text>
  
  <rect x="590" y="230" width="140" height="25" rx="4" ry="4" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1"/>
  <text x="600" y="247" font-family="Arial" font-size="12" fill="#334155">SettingsContext</text>
  
  <!-- Feature Contexts -->
  <rect x="760" y="130" width="160" height="200" rx="8" ry="8" fill="white" stroke="#bbf7d0" stroke-width="2" filter="url(#shadow)"/>
  <rect x="760" y="130" width="160" height="30" rx="8" ry="8" fill="url(#green-gradient)" stroke="#bbf7d0" stroke-width="0"/>
  <text x="840" y="150" font-family="Arial" font-size="14" font-weight="bold" text-anchor="middle" fill="white">Feature Contexts</text>
  
  <rect x="770" y="170" width="140" height="25" rx="4" ry="4" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1"/>
  <text x="780" y="187" font-family="Arial" font-size="12" fill="#334155">ThemeContext</text>
  
  <rect x="770" y="200" width="140" height="25" rx="4" ry="4" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1"/>
  <text x="780" y="217" font-family="Arial" font-size="12" fill="#334155">NotificationContext</text>
  
  <rect x="770" y="230" width="140" height="25" rx="4" ry="4" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1"/>
  <text x="780" y="247" font-family="Arial" font-size="12" fill="#334155">FirebaseContext</text>
  
  <rect x="770" y="260" width="140" height="25" rx="4" ry="4" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1"/>
  <text x="780" y="277" font-family="Arial" font-size="12" fill="#334155">TutorialContext</text>
  
  <!-- Services -->
  <rect x="50" y="390" width="400" height="280" rx="10" ry="10" fill="#fdf2f8" stroke="#fbcfe8" stroke-width="2"/>
  <text x="250" y="420" font-family="Arial" font-size="18" font-weight="bold" text-anchor="middle" fill="#db2777">Services</text>
  
  <!-- API Services -->
  <rect x="80" y="440" width="160" height="200" rx="8" ry="8" fill="white" stroke="#fbcfe8" stroke-width="2" filter="url(#shadow)"/>
  <rect x="80" y="440" width="160" height="30" rx="8" ry="8" fill="url(#purple-gradient)" stroke="#fbcfe8" stroke-width="0"/>
  <text x="160" y="460" font-family="Arial" font-size="14" font-weight="bold" text-anchor="middle" fill="white">API Services</text>
  
  <rect x="90" y="480" width="140" height="25" rx="4" ry="4" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1"/>
  <text x="100" y="497" font-family="Arial" font-size="12" fill="#334155">weatherService</text>
  
  <rect x="90" y="510" width="140" height="25" rx="4" ry="4" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1"/>
  <text x="100" y="527" font-family="Arial" font-size="12" fill="#334155">predictionService</text>
  
  <rect x="90" y="540" width="140" height="25" rx="4" ry="4" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1"/>
  <text x="100" y="557" font-family="Arial" font-size="12" fill="#334155">newsService</text>
  
  <rect x="90" y="570" width="140" height="25" rx="4" ry="4" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1"/>
  <text x="100" y="587" font-family="Arial" font-size="12" fill="#334155">chatbotService</text>
  
  <!-- Data Services -->
  <rect x="260" y="440" width="160" height="200" rx="8" ry="8" fill="white" stroke="#fbcfe8" stroke-width="2" filter="url(#shadow)"/>
  <rect x="260" y="440" width="160" height="30" rx="8" ry="8" fill="url(#purple-gradient)" stroke="#fbcfe8" stroke-width="0"/>
  <text x="340" y="460" font-family="Arial" font-size="14" font-weight="bold" text-anchor="middle" fill="white">Data Services</text>
  
  <rect x="270" y="480" width="140" height="25" rx="4" ry="4" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1"/>
  <text x="280" y="497" font-family="Arial" font-size="12" fill="#334155">supabaseClient</text>
  
  <rect x="270" y="510" width="140" height="25" rx="4" ry="4" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1"/>
  <text x="280" y="527" font-family="Arial" font-size="12" fill="#334155">firebaseService</text>
  
  <rect x="270" y="540" width="140" height="25" rx="4" ry="4" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1"/>
  <text x="280" y="557" font-family="Arial" font-size="12" fill="#334155">storageService</text>
  
  <rect x="270" y="570" width="140" height="25" rx="4" ry="4" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1"/>
  <text x="280" y="587" font-family="Arial" font-size="12" fill="#334155">analyticsService</text>
  
  <!-- Data Models -->
  <rect x="550" y="390" width="400" height="280" rx="10" ry="10" fill="#fff7ed" stroke="#fed7aa" stroke-width="2"/>
  <text x="750" y="420" font-family="Arial" font-size="18" font-weight="bold" text-anchor="middle" fill="#ea580c">Data Models</text>
  
  <!-- User Models -->
  <rect x="580" y="440" width="160" height="200" rx="8" ry="8" fill="white" stroke="#fed7aa" stroke-width="2" filter="url(#shadow)"/>
  <rect x="580" y="440" width="160" height="30" rx="8" ry="8" fill="url(#orange-gradient)" stroke="#fed7aa" stroke-width="0"/>
  <text x="660" y="460" font-family="Arial" font-size="14" font-weight="bold" text-anchor="middle" fill="white">User Models</text>
  
  <rect x="590" y="480" width="140" height="25" rx="4" ry="4" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1"/>
  <text x="600" y="497" font-family="Arial" font-size="12" fill="#334155">User</text>
  
  <rect x="590" y="510" width="140" height="25" rx="4" ry="4" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1"/>
  <text x="600" y="527" font-family="Arial" font-size="12" fill="#334155">Profile</text>
  
  <rect x="590" y="540" width="140" height="25" rx="4" ry="4" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1"/>
  <text x="600" y="557" font-family="Arial" font-size="12" fill="#334155">MedicalRecord</text>
  
  <rect x="590" y="570" width="140" height="25" rx="4" ry="4" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1"/>
  <text x="600" y="587" font-family="Arial" font-size="12" fill="#334155">Appointment</text>
  
  <!-- Social Models -->
  <rect x="760" y="440" width="160" height="200" rx="8" ry="8" fill="white" stroke="#fed7aa" stroke-width="2" filter="url(#shadow)"/>
  <rect x="760" y="440" width="160" height="30" rx="8" ry="8" fill="url(#orange-gradient)" stroke="#fed7aa" stroke-width="0"/>
  <text x="840" y="460" font-family="Arial" font-size="14" font-weight="bold" text-anchor="middle" fill="white">Social Models</text>
  
  <rect x="770" y="480" width="140" height="25" rx="4" ry="4" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1"/>
  <text x="780" y="497" font-family="Arial" font-size="12" fill="#334155">Post</text>
  
  <rect x="770" y="510" width="140" height="25" rx="4" ry="4" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1"/>
  <text x="780" y="527" font-family="Arial" font-size="12" fill="#334155">Comment</text>
  
  <rect x="770" y="540" width="140" height="25" rx="4" ry="4" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1"/>
  <text x="780" y="557" font-family="Arial" font-size="12" fill="#334155">ChatGroup</text>
  
  <rect x="770" y="570" width="140" height="25" rx="4" ry="4" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1"/>
  <text x="780" y="587" font-family="Arial" font-size="12" fill="#334155">Message</text>
  
  <rect x="770" y="600" width="140" height="25" rx="4" ry="4" fill="#f1f5f9" stroke="#e2e8f0" stroke-width="1"/>
  <text x="780" y="617" font-family="Arial" font-size="12" fill="#334155">Friendship</text>
  
  <!-- Relationships -->
  <!-- Frontend to Context -->
  <line x1="450" y1="180" x2="550" y2="180" stroke="#94a3b8" stroke-width="2" stroke-dasharray="5,5"/>
  <line x1="450" y1="240" x2="550" y2="240" stroke="#94a3b8" stroke-width="2" stroke-dasharray="5,5"/>
  
  <!-- Context to Services -->
  <line x1="660" y1="330" x2="250" y2="390" stroke="#94a3b8" stroke-width="2" stroke-dasharray="5,5"/>
  <line x1="840" y1="330" x2="250" y2="390" stroke="#94a3b8" stroke-width="2" stroke-dasharray="5,5"/>
  
  <!-- Services to Models -->
  <line x1="450" y1="490" x2="550" y2="490" stroke="#94a3b8" stroke-width="2" stroke-dasharray="5,5"/>
  <line x1="450" y1="550" x2="550" y2="550" stroke="#94a3b8" stroke-width="2" stroke-dasharray="5,5"/>
</svg>
