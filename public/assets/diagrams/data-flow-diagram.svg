<?xml version="1.0" encoding="UTF-8"?>
<svg width="1000" height="700" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Gradient Definitions -->
    <linearGradient id="blue-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3a86ff" stop-opacity="1"/>
      <stop offset="100%" stop-color="#0072ff" stop-opacity="1"/>
    </linearGradient>
    <linearGradient id="purple-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#8338ec" stop-opacity="1"/>
      <stop offset="100%" stop-color="#5e17eb" stop-opacity="1"/>
    </linearGradient>
    <linearGradient id="green-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#10b981" stop-opacity="1"/>
      <stop offset="100%" stop-color="#059669" stop-opacity="1"/>
    </linearGradient>
    <linearGradient id="orange-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#fb923c" stop-opacity="1"/>
      <stop offset="100%" stop-color="#ea580c" stop-opacity="1"/>
    </linearGradient>
    
    <!-- Shadow Filter -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000000" flood-opacity="0.2"/>
    </filter>
    
    <!-- Arrow Marker -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="0" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#64748b"/>
    </marker>
  </defs>
  
  <!-- Background -->
  <rect width="1000" height="700" fill="#f8fafc" rx="10" ry="10"/>
  <text x="500" y="40" font-family="Arial" font-size="24" font-weight="bold" text-anchor="middle" fill="#1e293b">Ukuqala Data Flow Diagram</text>
  
  <!-- User Data Flow -->
  <text x="500" y="80" font-family="Arial" font-size="18" font-weight="bold" text-anchor="middle" fill="#1e293b">User Data Flow</text>
  
  <!-- User Input -->
  <rect x="100" y="100" width="120" height="60" rx="10" ry="10" fill="white" stroke="#cbd5e1" stroke-width="2" filter="url(#shadow)"/>
  <text x="160" y="135" font-family="Arial" font-size="14" text-anchor="middle" fill="#334155">User Input</text>
  
  <!-- Form Validation -->
  <rect x="300" y="100" width="120" height="60" rx="10" ry="10" fill="white" stroke="#cbd5e1" stroke-width="2" filter="url(#shadow)"/>
  <text x="360" y="135" font-family="Arial" font-size="14" text-anchor="middle" fill="#334155">Form Validation</text>
  
  <!-- Supabase API -->
  <rect x="500" y="100" width="120" height="60" rx="10" ry="10" fill="url(#blue-gradient)" stroke="#bfdbfe" stroke-width="2" filter="url(#shadow)"/>
  <text x="560" y="135" font-family="Arial" font-size="14" text-anchor="middle" fill="white">Supabase API</text>
  
  <!-- PostgreSQL -->
  <rect x="700" y="100" width="120" height="60" rx="10" ry="10" fill="url(#blue-gradient)" stroke="#bfdbfe" stroke-width="2" filter="url(#shadow)"/>
  <text x="760" y="135" font-family="Arial" font-size="14" text-anchor="middle" fill="white">PostgreSQL</text>
  
  <!-- Arrows -->
  <line x1="220" y1="130" x2="300" y2="130" stroke="#64748b" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="420" y1="130" x2="500" y2="130" stroke="#64748b" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="620" y1="130" x2="700" y2="130" stroke="#64748b" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Social Data Flow -->
  <text x="500" y="200" font-family="Arial" font-size="18" font-weight="bold" text-anchor="middle" fill="#1e293b">Social Data Flow</text>
  
  <!-- Social Interaction -->
  <rect x="100" y="220" width="120" height="60" rx="10" ry="10" fill="white" stroke="#cbd5e1" stroke-width="2" filter="url(#shadow)"/>
  <text x="160" y="255" font-family="Arial" font-size="14" text-anchor="middle" fill="#334155">Social Interaction</text>
  
  <!-- Firebase SDK -->
  <rect x="300" y="220" width="120" height="60" rx="10" ry="10" fill="url(#orange-gradient)" stroke="#fed7aa" stroke-width="2" filter="url(#shadow)"/>
  <text x="360" y="255" font-family="Arial" font-size="14" text-anchor="middle" fill="white">Firebase SDK</text>
  
  <!-- Firestore -->
  <rect x="500" y="220" width="120" height="60" rx="10" ry="10" fill="url(#orange-gradient)" stroke="#fed7aa" stroke-width="2" filter="url(#shadow)"/>
  <text x="560" y="255" font-family="Arial" font-size="14" text-anchor="middle" fill="white">Firestore</text>
  
  <!-- Real-time Updates -->
  <rect x="700" y="220" width="120" height="60" rx="10" ry="10" fill="white" stroke="#cbd5e1" stroke-width="2" filter="url(#shadow)"/>
  <text x="760" y="248" font-family="Arial" font-size="14" text-anchor="middle" fill="#334155">Real-time</text>
  <text x="760" y="268" font-family="Arial" font-size="14" text-anchor="middle" fill="#334155">Updates</text>
  
  <!-- Arrows -->
  <line x1="220" y1="250" x2="300" y2="250" stroke="#64748b" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="420" y1="250" x2="500" y2="250" stroke="#64748b" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="620" y1="250" x2="700" y2="250" stroke="#64748b" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Bidirectional arrow -->
  <line x1="760" y1="280" x2="760" y2="310" stroke="#64748b" stroke-width="2"/>
  <line x1="760" y1="310" x2="160" y2="310" stroke="#64748b" stroke-width="2"/>
  <line x1="160" y1="310" x2="160" y2="280" stroke="#64748b" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="450" y="305" font-family="Arial" font-size="12" text-anchor="middle" fill="#64748b">Real-time Data Sync</text>
  
  <!-- Prediction Data Flow -->
  <text x="500" y="350" font-family="Arial" font-size="18" font-weight="bold" text-anchor="middle" fill="#1e293b">Prediction Data Flow</text>
  
  <!-- Symptom Input -->
  <rect x="100" y="370" width="120" height="60" rx="10" ry="10" fill="white" stroke="#cbd5e1" stroke-width="2" filter="url(#shadow)"/>
  <text x="160" y="405" font-family="Arial" font-size="14" text-anchor="middle" fill="#334155">Symptom Input</text>
  
  <!-- FastAPI Backend -->
  <rect x="300" y="370" width="120" height="60" rx="10" ry="10" fill="url(#green-gradient)" stroke="#bbf7d0" stroke-width="2" filter="url(#shadow)"/>
  <text x="360" y="398" font-family="Arial" font-size="14" text-anchor="middle" fill="white">FastAPI</text>
  <text x="360" y="418" font-family="Arial" font-size="14" text-anchor="middle" fill="white">Backend</text>
  
  <!-- ML Models -->
  <rect x="500" y="370" width="120" height="60" rx="10" ry="10" fill="url(#purple-gradient)" stroke="#e9d5ff" stroke-width="2" filter="url(#shadow)"/>
  <text x="560" y="405" font-family="Arial" font-size="14" text-anchor="middle" fill="white">ML Models</text>
  
  <!-- Prediction Results -->
  <rect x="700" y="370" width="120" height="60" rx="10" ry="10" fill="white" stroke="#cbd5e1" stroke-width="2" filter="url(#shadow)"/>
  <text x="760" y="398" font-family="Arial" font-size="14" text-anchor="middle" fill="#334155">Prediction</text>
  <text x="760" y="418" font-family="Arial" font-size="14" text-anchor="middle" fill="#334155">Results</text>
  
  <!-- Arrows -->
  <line x1="220" y1="400" x2="300" y2="400" stroke="#64748b" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="420" y1="400" x2="500" y2="400" stroke="#64748b" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="620" y1="400" x2="700" y2="400" stroke="#64748b" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Return arrow -->
  <line x1="760" y1="430" x2="760" y2="460" stroke="#64748b" stroke-width="2"/>
  <line x1="760" y1="460" x2="160" y2="460" stroke="#64748b" stroke-width="2"/>
  <line x1="160" y1="460" x2="160" y2="430" stroke="#64748b" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="450" y="455" font-family="Arial" font-size="12" text-anchor="middle" fill="#64748b">Display Results to User</text>
  
  <!-- Notification Data Flow -->
  <text x="500" y="500" font-family="Arial" font-size="18" font-weight="bold" text-anchor="middle" fill="#1e293b">Notification Data Flow</text>
  
  <!-- Event Trigger -->
  <rect x="100" y="520" width="120" height="60" rx="10" ry="10" fill="white" stroke="#cbd5e1" stroke-width="2" filter="url(#shadow)"/>
  <text x="160" y="555" font-family="Arial" font-size="14" text-anchor="middle" fill="#334155">Event Trigger</text>
  
  <!-- Edge Function -->
  <rect x="300" y="520" width="120" height="60" rx="10" ry="10" fill="url(#blue-gradient)" stroke="#bfdbfe" stroke-width="2" filter="url(#shadow)"/>
  <text x="360" y="548" font-family="Arial" font-size="14" text-anchor="middle" fill="white">Edge</text>
  <text x="360" y="568" font-family="Arial" font-size="14" text-anchor="middle" fill="white">Function</text>
  
  <!-- Africa's Talking API -->
  <rect x="500" y="520" width="120" height="60" rx="10" ry="10" fill="white" stroke="#cbd5e1" stroke-width="2" filter="url(#shadow)"/>
  <text x="560" y="548" font-family="Arial" font-size="14" text-anchor="middle" fill="#334155">Africa's</text>
  <text x="560" y="568" font-family="Arial" font-size="14" text-anchor="middle" fill="#334155">Talking API</text>
  
  <!-- SMS Delivery -->
  <rect x="700" y="520" width="120" height="60" rx="10" ry="10" fill="white" stroke="#cbd5e1" stroke-width="2" filter="url(#shadow)"/>
  <text x="760" y="555" font-family="Arial" font-size="14" text-anchor="middle" fill="#334155">SMS Delivery</text>
  
  <!-- Arrows -->
  <line x1="220" y1="550" x2="300" y2="550" stroke="#64748b" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="420" y1="550" x2="500" y2="550" stroke="#64748b" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="620" y1="550" x2="700" y2="550" stroke="#64748b" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Firebase Cloud Messaging -->
  <rect x="300" y="620" width="320" height="60" rx="10" ry="10" fill="url(#orange-gradient)" stroke="#fed7aa" stroke-width="2" filter="url(#shadow)"/>
  <text x="460" y="655" font-family="Arial" font-size="14" text-anchor="middle" fill="white">Firebase Cloud Messaging</text>
  
  <!-- Arrows -->
  <line x1="160" y1="580" x2="160" y2="650" stroke="#64748b" stroke-width="2"/>
  <line x1="160" y1="650" x2="300" y2="650" stroke="#64748b" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="230" y="640" font-family="Arial" font-size="12" text-anchor="middle" fill="#64748b">In-app Notifications</text>
  
  <line x1="620" y1="650" x2="760" y2="650" stroke="#64748b" stroke-width="2"/>
  <line x1="760" y1="650" x2="760" y2="580" stroke="#64748b" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="690" y="640" font-family="Arial" font-size="12" text-anchor="middle" fill="#64748b">Push Notifications</text>
</svg>
