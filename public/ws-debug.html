<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Debug Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        .connection-info { border: 1px solid #ddd; padding: 15px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>WebSocket Debug Tool</h1>
    
    <div class="connection-info">
        <h3>Connection Information</h3>
        <p><strong>Current URL:</strong> <span id="currentUrl"></span></p>
        <p><strong>Protocol:</strong> <span id="protocol"></span></p>
        <p><strong>Host:</strong> <span id="host"></span></p>
        <p><strong>Port:</strong> <span id="port"></span></p>
    </div>
    
    <button onclick="testWebSocket()">Test WebSocket Connection</button>
    <button onclick="testViteHMR()">Test Vite HMR</button>
    <button onclick="checkBrowserSupport()">Check Browser Support</button>
    <button onclick="clearLog()">Clear Log</button>
    
    <div id="output"></div>

    <script>
        // Display current connection info
        document.getElementById('currentUrl').textContent = window.location.href;
        document.getElementById('protocol').textContent = window.location.protocol;
        document.getElementById('host').textContent = window.location.hostname;
        document.getElementById('port').textContent = window.location.port || '80';

        function log(message, type = 'info') {
            const output = document.getElementById('output');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            output.appendChild(div);
            output.scrollTop = output.scrollHeight;
        }

        function clearLog() {
            document.getElementById('output').innerHTML = '';
        }

        function checkBrowserSupport() {
            log('Checking browser support...');
            
            // WebSocket support
            if ('WebSocket' in window) {
                log('✅ WebSocket is supported', 'success');
            } else {
                log('❌ WebSocket is not supported', 'error');
            }

            // EventSource support (for SSE fallback)
            if ('EventSource' in window) {
                log('✅ EventSource (SSE) is supported', 'success');
            } else {
                log('❌ EventSource (SSE) is not supported', 'warning');
            }

            // Browser info
            log(`Browser: ${navigator.userAgent}`, 'info');
            log(`Secure Context: ${window.isSecureContext}`, 'info');
        }

        function testWebSocket() {
            log('Testing WebSocket connection...');
            
            const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsHost = window.location.hostname;
            const wsPort = window.location.port || '3000';
            
            // Test different WebSocket URLs
            const testUrls = [
                `${wsProtocol}//${wsHost}:${wsPort}`,
                `${wsProtocol}//localhost:3000`,
                `${wsProtocol}//127.0.0.1:3000`,
                `${wsProtocol}//${wsHost}:3001`, // HMR port
                `${wsProtocol}//localhost:3001`
            ];

            testUrls.forEach((url, index) => {
                setTimeout(() => {
                    testSingleWebSocket(url);
                }, index * 1000); // Stagger tests
            });
        }

        function testSingleWebSocket(url) {
            log(`Testing WebSocket: ${url}`);
            
            const ws = new WebSocket(url);
            const timeout = setTimeout(() => {
                ws.close();
                log(`❌ WebSocket timeout: ${url}`, 'error');
            }, 5000);

            ws.onopen = function() {
                clearTimeout(timeout);
                log(`✅ WebSocket connected: ${url}`, 'success');
                ws.close();
            };

            ws.onerror = function(error) {
                clearTimeout(timeout);
                log(`❌ WebSocket error: ${url} - ${error.type}`, 'error');
            };

            ws.onclose = function(event) {
                clearTimeout(timeout);
                if (event.wasClean) {
                    log(`WebSocket closed cleanly: ${url}`, 'info');
                } else {
                    log(`❌ WebSocket closed unexpectedly: ${url} (Code: ${event.code})`, 'error');
                }
            };
        }

        function testViteHMR() {
            log('Testing Vite HMR connection...');
            
            // Try to connect to Vite's HMR WebSocket
            const viteUrls = [
                `ws://localhost:3000`,
                `ws://127.0.0.1:3000`,
                `ws://localhost:3001`,
                `ws://127.0.0.1:3001`
            ];

            viteUrls.forEach((baseUrl, index) => {
                setTimeout(() => {
                    // Simulate Vite HMR connection
                    const ws = new WebSocket(`${baseUrl}/?token=test`);
                    
                    const timeout = setTimeout(() => {
                        ws.close();
                        log(`❌ Vite HMR timeout: ${baseUrl}`, 'error');
                    }, 3000);

                    ws.onopen = function() {
                        clearTimeout(timeout);
                        log(`✅ Vite HMR connected: ${baseUrl}`, 'success');
                        ws.close();
                    };

                    ws.onerror = function(error) {
                        clearTimeout(timeout);
                        log(`❌ Vite HMR error: ${baseUrl}`, 'error');
                    };

                    ws.onclose = function(event) {
                        clearTimeout(timeout);
                        if (!event.wasClean) {
                            log(`❌ Vite HMR connection failed: ${baseUrl} (Code: ${event.code})`, 'error');
                        }
                    };
                }, index * 500);
            });
        }

        // Auto-run browser support check
        window.addEventListener('load', () => {
            checkBrowserSupport();
            log('WebSocket debug tool loaded. Click buttons above to run tests.', 'info');
        });
    </script>
</body>
</html>
