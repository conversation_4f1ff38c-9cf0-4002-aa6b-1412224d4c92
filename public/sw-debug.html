<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Service Worker Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Service Worker Debug Tool</h1>
    
    <div id="status"></div>
    
    <button onclick="checkStatus()">Check Status</button>
    <button onclick="unregisterAll()">Unregister All</button>
    <button onclick="clearCaches()">Clear Caches</button>
    <button onclick="testFiles()">Test SW Files</button>
    
    <div id="output"></div>

    <script>
        function log(message, type = 'info') {
            const output = document.getElementById('output');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            output.appendChild(div);
        }

        async function checkStatus() {
            log('Checking service worker status...');
            
            if (!('serviceWorker' in navigator)) {
                log('Service workers not supported', 'error');
                return;
            }

            try {
                const registrations = await navigator.serviceWorker.getRegistrations();
                log(`Found ${registrations.length} service worker registrations`);
                
                registrations.forEach((reg, index) => {
                    log(`Registration ${index + 1}: ${reg.scope}`, 'success');
                    if (reg.active) {
                        log(`  Active: ${reg.active.scriptURL}`, 'success');
                    }
                    if (reg.waiting) {
                        log(`  Waiting: ${reg.waiting.scriptURL}`, 'warning');
                    }
                    if (reg.installing) {
                        log(`  Installing: ${reg.installing.scriptURL}`, 'warning');
                    }
                });
            } catch (error) {
                log(`Error: ${error.message}`, 'error');
            }
        }

        async function unregisterAll() {
            log('Unregistering all service workers...');
            
            try {
                const registrations = await navigator.serviceWorker.getRegistrations();
                const results = await Promise.all(
                    registrations.map(reg => reg.unregister())
                );
                log(`Unregistered ${results.filter(r => r).length} service workers`, 'success');
            } catch (error) {
                log(`Error: ${error.message}`, 'error');
            }
        }

        async function clearCaches() {
            log('Clearing all caches...');
            
            try {
                const cacheNames = await caches.keys();
                await Promise.all(
                    cacheNames.map(name => caches.delete(name))
                );
                log(`Cleared ${cacheNames.length} caches`, 'success');
            } catch (error) {
                log(`Error: ${error.message}`, 'error');
            }
        }

        async function testFiles() {
            log('Testing service worker files...');
            
            const files = ['/service-worker.js', '/firebase-messaging-sw.js'];
            
            for (const file of files) {
                try {
                    const response = await fetch(file, { cache: 'no-cache' });
                    const status = response.ok ? 'success' : 'error';
                    log(`${file}: ${response.status} ${response.statusText}`, status);
                } catch (error) {
                    log(`${file}: ${error.message}`, 'error');
                }
            }
        }

        // Auto-check status on load
        window.addEventListener('load', checkStatus);
    </script>
</body>
</html>
