<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Firebase Messaging Service Worker</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100vh;
      margin: 0;
      padding: 20px;
      text-align: center;
      color: #333;
      background-color: #f9f9f9;
    }
    .container {
      max-width: 600px;
      padding: 30px;
      background-color: white;
      border-radius: 10px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    h1 {
      color: #4285F4;
      margin-bottom: 20px;
    }
    p {
      line-height: 1.6;
      margin-bottom: 15px;
    }
    .logo {
      width: 80px;
      height: 80px;
      margin-bottom: 20px;
    }
  </style>
</head>
<body>
  <div class="container">
    <img src="/logo192.png" alt="CareAI Logo" class="logo">
    <h1>Firebase Messaging Service Worker</h1>
    <p>This page is part of the CareAI application's push notification system.</p>
    <p>The service worker is running in the background to handle push notifications.</p>
    <p>You can close this page and return to the main application.</p>
    <p><a href="/">Return to CareAI</a></p>
  </div>
</body>
</html>
