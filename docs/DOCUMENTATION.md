# CareAI Documentation Guide

This document provides an overview of the documentation available for the CareAI project.

## Documentation Structure

The documentation is organized into the following categories:

### Project Overview
- `README.md` - Main project documentation
- `project-structure.md` - Detailed project structure information
- `getting-started.md` - Getting started guide

### Feature Documentation
- `Analytics.md` - Analytics feature documentation
- `SleepProgram.md` - Sleep tracking feature documentation
- `SOCIAL_FEATURES_README.md` - Social features documentation
- `advanced-features.md` / `ADVANCED_FEATURES.md` - Advanced features documentation

### Technical Documentation
- `NEW_MODEL.md` - Documentation for adding new ML models
- `README-FIREBASE-INDEXES.md` - Firebase indexes configuration
- `firebase-notifications-fix.md` - Firebase notifications troubleshooting

### Fixes and Updates
- `README-SOCIAL-FIXES.md` - Social features fixes documentation
- `README_NEW_FEATURES.md` - New features documentation

## Documentation Standards

When contributing to documentation:

1. Use clear, descriptive filenames
2. Include a title and overview at the beginning of each document
3. Use markdown formatting for better readability
4. Include code examples where appropriate
5. Add links to related documentation

## Available Documentation Files

| File | Description |
|------|-------------|
| `README.md` | Main project documentation |
| `advanced-features.md` | Advanced features documentation |
| `ADVANCED_FEATURES.md` | Updated advanced features documentation |
| `Analytics.md` | Analytics feature documentation |
| `firebase-notifications-fix.md` | Firebase notifications troubleshooting |
| `getting-started.md` | Getting started guide |
| `index.md` | Documentation index |
| `NEW_MODEL.md` | Documentation for adding new ML models |
| `project-structure.md` | Detailed project structure information |
| `README-FIREBASE-INDEXES.md` | Firebase indexes configuration |
| `README_NEW_FEATURES.md` | New features documentation |
| `README-SOCIAL-FIXES.md` | Social features fixes documentation |
| `SleepProgram.md` | Sleep tracking feature documentation |
| `SOCIAL_FEATURES_README.md` | Social features documentation |

## Contributing to Documentation

When adding new documentation:

1. Follow the existing naming conventions
2. Update this guide to include your new documentation
3. Link to your documentation from related documents
4. Include examples and screenshots where appropriate
