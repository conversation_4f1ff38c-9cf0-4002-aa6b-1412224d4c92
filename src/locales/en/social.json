{"social": {"feed": "Social Feed", "socialFeed": "Social Feed", "createPost": "Create Post", "editPost": "Edit Post", "post": "Post", "posting": "Posting...", "whatsOnYourMind": "What's on your mind?", "addImage": "Add Image", "like": "Like", "comment": "Comment", "share": "Share", "copyLink": "Copy Link", "edit": "Edit", "delete": "Delete", "recent": "Recent", "trending": "Trending", "top": "Top", "writeComment": "Write a comment...", "hashtags": "Hashtags", "addHashtags": "Add hashtags...", "hashtagsRequired": "At least one hashtag is required", "postAnonymously": "Post anonymously", "notifications": "Notifications", "markAllAsRead": "Mark all as read", "noNotifications": "No notifications yet", "errorCreatingComment": "Error creating comment", "checkingSetup": "Checking social features setup...", "setupRequired": "Social Features Setup Required", "socialFeaturesNotSetup": "The social features in your CareAI application are not fully set up.", "socialFeaturesNotSetupDetails": "To use the social features in CareAI, you need to set up the database tables and permissions. Follow the instructions below to set up the social features.", "viewSetupInstructions": "View Setup Instructions", "socialFeaturesSetup": "Social Features Setup", "setupStatus": "Current Setup Status", "tablesFound": "Tables found", "storageBucket": "Storage bucket", "storedProcedures": "Stored procedures", "setupInstructions": "Setup Instructions", "setupInstructionsDetails": "Follow these steps to set up the social features:", "setupStep1": "Log in to your Supabase dashboard", "setupStep2": "Go to the SQL Editor", "setupStep3": "Create a new query", "setupStep4": "Copy and paste the SQL script from the fix_social_features.sql file", "setupStep5": "Run the query", "setupStep6": "Restart your application", "setupNote": "After running the script, you can check if the social features are properly set up by refreshing this page.", "runSetup": "Run Setup", "checkAgain": "Check Again"}}