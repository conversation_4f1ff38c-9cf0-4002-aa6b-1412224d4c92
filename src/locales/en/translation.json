{"common": {"appName": "CareAI", "welcome": "Welcome to CareAI", "loading": "Loading...", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "remove": "Remove", "search": "Search", "settings": "Settings", "profile": "Profile", "logout": "Logout", "login": "<PERSON><PERSON>", "register": "Register", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "name": "Name", "phone": "Phone", "address": "Address", "dateOfBirth": "Date of Birth", "yes": "Yes", "no": "No", "back": "Back", "next": "Next", "submit": "Submit", "continue": "Continue", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "close": "Close", "processing": "Processing...", "posting": "Posting...", "home": "Home", "predictions": "Predictions", "appointments": "Appointments", "healthTips": "Health Tips", "weather": "Weather", "chatbot": "Medical Chatbot", "analytics": "Analytics", "nutrition": "Nutrition", "sleep": "Sleep", "exercise": "Exercise", "mental": "Mental Health", "dashboard": "Dashboard", "viewAll": "View All", "compareWithPrevious": "Compare", "lightMode": "Light Mode", "darkMode": "Dark Mode", "systemPreference": "System Preference", "appearance": "Appearance", "language": "Language", "english": "English", "spanish": "Spanish", "french": "French", "notifications": "Notifications", "security": "Security", "privacy": "Privacy", "accessibility": "Accessibility", "about": "About", "help": "Help", "deleteAccount": "Delete Account", "refresh": "Refresh", "version": "Version", "termsOfService": "Terms of Service", "privacyPolicy": "Privacy Policy", "contactUs": "Contact Us", "feedback": "<PERSON><PERSON><PERSON>", "reportIssue": "Report Issue", "highContrast": "High Contrast", "fontSize": "Font Size", "small": "Small", "medium": "Medium", "large": "Large", "extraLarge": "Extra Large", "animations": "Animations", "on": "On", "off": "Off", "enable": "Enable", "disable": "Disable", "enabled": "Enabled", "disabled": "Disabled", "goBack": "Go Back"}, "auth": {"signIn": "Sign In", "signUp": "Sign Up", "forgotPassword": "Forgot Password?", "resetPassword": "Reset Password", "sendResetLink": "Send Reset Link", "alreadyHaveAccount": "Already have an account?", "dontHaveAccount": "Don't have an account?", "signInWithGoogle": "Sign in with Google", "signUpWithGoogle": "Sign up with Google", "orContinueWith": "Or continue with", "rememberMe": "Remember me", "verifyEmail": "<PERSON><PERSON><PERSON>", "verificationCodeSent": "A verification code has been sent to your email", "enterVerificationCode": "Enter verification code", "resendCode": "Resend code", "verifyAccount": "Verify Account", "accountVerified": "Your account has been verified", "passwordResetSent": "Password reset link has been sent to your email", "passwordResetSuccess": "Your password has been reset successfully", "invalidCredentials": "Invalid email or password", "emailAlreadyInUse": "Email is already in use", "weakPassword": "Password is too weak", "passwordMismatch": "Passwords do not match", "enterPin": "Enter your PIN", "setupPin": "Set up your PIN", "confirmPin": "Confirm your PIN", "pinMismatch": "PINs do not match", "pinRequired": "PIN is required", "invalidPin": "Invalid PIN", "pinSetupSuccess": "PIN has been set up successfully", "twoFactorAuth": "Two-Factor Authentication", "enableTwoFactor": "Enable Two-Factor Authentication", "disableTwoFactor": "Disable Two-Factor Authentication", "scanQrCode": "Scan this QR code with your authenticator app", "enterAuthCode": "Enter the code from your authenticator app", "twoFactorEnabled": "Two-Factor Authentication has been enabled", "twoFactorDisabled": "Two-Factor Authentication has been disabled"}, "home": {"welcome": "Welcome, {{name}}", "goodMorning": "Good morning, {{name}}", "goodAfternoon": "Good afternoon, {{name}}", "goodEvening": "Good evening, {{name}}", "overview": "Overview", "recentPredictions": "Recent Predictions", "upcomingAppointments": "Upcoming Appointments", "healthMetrics": "Health Metrics", "viewAll": "View All", "todayAt": "Today at {{time}}", "tomorrowAt": "Tomorrow at {{time}}", "yesterday": "Yesterday", "noAppointments": "No upcoming appointments", "noPredictions": "No recent predictions", "scheduleAppointment": "Schedule Appointment", "makePrediction": "Make Prediction"}, "predictions": {"title": "Health Predictions", "makeNewPrediction": "Make New Prediction", "recentPredictions": "Recent Predictions", "predictionHistory": "Prediction History", "heartDisease": "Heart Disease", "diabetes": "Diabetes", "brainCancer": "Brain Cancer", "skinCancer": "Skin Cancer", "symptoms": "Symptoms Analysis", "riskLevel": "Risk Level", "highRisk": "High Risk", "moderateRisk": "Moderate Risk", "lowRisk": "Low Risk", "unknown": "Unknown", "probability": "Probability", "recommendations": "Recommendations", "consultDoctor": "Consult a doctor", "regularCheckups": "Regular checkups recommended", "healthyLifestyle": "Maintain a healthy lifestyle", "uploadImage": "Upload Image", "dragAndDrop": "Drag and drop an image here, or click to select", "analyzing": "Analyzing...", "selectSymptoms": "Select your symptoms", "searchSymptoms": "Search symptoms", "selectedSymptoms": "Selected symptoms", "noSymptomsSelected": "No symptoms selected", "analyzeSymptoms": "Analyze Symptoms", "possibleConditions": "Possible Conditions", "confidence": "Confidence", "printReport": "Print Report", "saveResult": "Save Result", "compareWithPrevious": "Compare with Previous", "noDataAvailable": "No data available for comparison", "parameter": "Parameter", "change": "Change"}, "appointments": {"title": "Appointments", "schedule": "Schedule Appointment", "upcoming": "Upcoming Appointments", "past": "Past Appointments", "noUpcoming": "No upcoming appointments", "noPast": "No past appointments", "selectDate": "Select Date", "selectTime": "Select Time", "selectDoctor": "Select Doctor", "selectReason": "Select Reason", "appointmentDetails": "Appointment Details", "reason": "Reason", "notes": "Notes", "addNotes": "Add notes", "confirmAppointment": "Confirm Appointment", "reschedule": "Reschedule", "cancel": "<PERSON>cel Appointment", "cancelled": "Cancelled", "confirmed": "Confirmed", "pending": "Pending", "completed": "Completed", "appointmentConfirmed": "Your appointment has been confirmed", "appointmentCancelled": "Your appointment has been cancelled", "appointmentRescheduled": "Your appointment has been rescheduled", "reminderSet": "Reminder set for {{time}} before appointment", "setReminder": "<PERSON>minder", "videoConsultation": "Video Consultation", "inPerson": "In Person", "consultationType": "Consultation Type"}, "tracking": {"title": "Health Tracking", "sleep": "Sleep", "water": "Water", "nutrition": "Nutrition", "exercise": "Exercise", "medication": "Medication", "weight": "Weight", "bloodPressure": "Blood Pressure", "bloodSugar": "Blood Sugar", "addEntry": "Add Entry", "editEntry": "Edit Entry", "deleteEntry": "Delete Entry", "noEntries": "No entries found", "today": "Today", "yesterday": "Yesterday", "thisWeek": "This Week", "lastWeek": "Last Week", "thisMonth": "This Month", "custom": "Custom", "from": "From", "to": "To", "apply": "Apply", "reset": "Reset", "hours": "Hours", "minutes": "Minutes", "sleepQuality": "Sleep Quality", "poor": "Poor", "fair": "Fair", "good": "Good", "excellent": "Excellent", "bedtime": "Bedtime", "wakeup": "Wake Up", "duration": "Duration", "waterIntake": "Water Intake", "glasses": "Glasses", "ml": "ml", "oz": "oz", "addWater": "Add Water", "mealType": "Meal Type", "breakfast": "Breakfast", "lunch": "Lunch", "dinner": "Dinner", "snack": "Snack", "calories": "Calories", "protein": "<PERSON><PERSON>", "carbs": "<PERSON><PERSON>", "fat": "Fat", "addMeal": "<PERSON><PERSON>", "exerciseType": "Exercise Type", "cardio": "Cardio", "strength": "Strength", "flexibility": "Flexibility", "other": "Other", "intensity": "Intensity", "low": "Low", "medium": "Medium", "high": "High", "caloriesBurned": "Calories Burned", "addExercise": "Add Exercise", "medicationName": "Medication Name", "dosage": "Dosage", "frequency": "Frequency", "taken": "Taken", "missed": "Missed", "addMedication": "Add Medication", "currentWeight": "Current Weight", "targetWeight": "Target Weight", "kg": "kg", "lb": "lb", "addWeight": "Add Weight", "systolic": "Systolic", "diastolic": "Diastolic", "pulse": "Pulse", "addBloodPressure": "Add Blood Pressure", "glucoseLevel": "Glucose Level", "beforeMeal": "Before Meal", "afterMeal": "After Meal", "fasting": "Fasting", "random": "Random", "addBloodSugar": "Add Blood Sugar"}, "chatbot": {"title": "Medical Chatbot", "welcome": "Hello! I'm your medical assistant. How can I help you today?", "disclaimer": "This is not a substitute for professional medical advice. Please consult a healthcare provider for medical concerns.", "typeMessage": "Type your message...", "send": "Send", "thinking": "Thinking...", "errorMessage": "Sorry, I couldn't process your request. Please try again.", "suggestedQuestions": "Suggested Questions", "clearChat": "Clear Chat", "startNewChat": "Start New Chat", "saveConversation": "Save Conversation", "loadingResponse": "Loading response...", "askAboutSymptoms": "Ask about symptoms", "askAboutMedications": "Ask about medications", "askAboutPrevention": "Ask about prevention", "askAboutTreatment": "Ask about treatment"}, "profile": {"title": "Profile", "personalInfo": "Personal Information", "medicalInfo": "Medical Information", "accountSettings": "Account <PERSON><PERSON>", "editProfile": "Edit Profile", "changePassword": "Change Password", "deleteAccount": "Delete Account", "currentPassword": "Current Password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "passwordChanged": "Password changed successfully", "accountDeleted": "Your account has been deleted", "deleteConfirmation": "Are you sure you want to delete your account? This action cannot be undone.", "uploadPhoto": "Upload Photo", "removePhoto": "Remove Photo", "bloodType": "Blood Type", "height": "Height", "weight": "Weight", "allergies": "Allergies", "medications": "Medications", "conditions": "Medical Conditions", "emergencyContact": "Emergency Contact", "relationship": "Relationship", "saveChanges": "Save Changes", "discardChanges": "Discard Changes", "profileUpdated": "Profile updated successfully", "personalInformation": "Personal Information", "medicalInformation": "Medical Information", "securitySettings": "Security Settings", "saveProfile": "Save Profile", "cancelEditing": "Cancel Editing", "fullName": "Full Name", "emailAddress": "Email Address", "phoneNumber": "Phone Number", "age": "Age", "gender": "Gender", "male": "Male", "female": "Female", "other": "Other", "preferNotToSay": "Prefer not to say", "bloodGroup": "Blood Group", "medicalConditions": "Medical Conditions", "familyHistory": "Family History", "relationshipToYou": "Relationship to You", "profileCompleteness": "Profile Completeness", "completeYourProfile": "Complete Your Profile", "profileUpdateFailed": "Failed to Update Profile", "confirmDeleteAccount": "Are you sure you want to delete your account?", "deleteAccountWarning": "This action cannot be undone. All your data will be permanently deleted.", "deleteAccountIrreversible": "This is an irreversible action. Please type DELETE to confirm.", "typeToConfirm": "Type {text} to confirm", "permanentlyDelete": "Permanently Delete"}, "settings": {"title": "Settings", "general": "General", "account": "Account", "privacy": "Privacy", "notifications": "Notifications", "appearance": "Appearance", "language": "Language", "languageDesc": "Select your preferred language", "theme": "Theme", "themeDesc": "Choose light or dark mode", "light": "Light", "dark": "Dark", "system": "System Default", "timezone": "Timezone", "timezoneDesc": "Set your local timezone", "dataPrivacy": "Data Privacy", "dataPrivacyDesc": "Manage how your data is used", "dataSharing": "Data Sharing", "dataSharingDesc": "Control what data is shared", "exportData": "Export Your Data", "exportDataDesc": "Download a copy of your data", "deleteData": "Delete Your Data", "deleteDataDesc": "Permanently delete your data", "pushNotifications": "Push Notifications", "pushNotificationsDesc": "Receive alerts on your device", "emailNotifications": "Email Notifications", "emailNotificationsDesc": "Receive updates via email", "appointmentReminders": "Appointment Reminders", "appointmentRemindersDesc": "Get notified about upcoming appointments", "medicationReminders": "Medication Reminders", "medicationRemindersDesc": "Get reminded to take your medications", "healthAlerts": "Health Alerts", "healthAlertsDesc": "Receive alerts about your health", "accessibility": "Accessibility", "accessibilityDesc": "Customize your experience", "fontSize": "Font Size", "fontSizeDesc": "Adjust text size", "contrastMode": "Contrast Mode", "contrastModeDesc": "Improve readability", "normal": "Normal", "highContrast": "High Contrast", "animations": "Animations", "animationsDesc": "Control motion effects", "about": "About", "aboutDesc": "Learn more about CareAI", "version": "Version", "termsOfService": "Terms of Service", "privacyPolicy": "Privacy Policy", "help": "Help & Support", "helpDesc": "Get assistance", "contactSupport": "Contact Support", "faq": "FAQ", "feedback": "Send Feedback", "generalSettings": "General Settings", "accountSettings": "Account <PERSON><PERSON>", "themeSettings": "Theme Settings", "notificationSettings": "Notification Settings", "securitySettings": "Security Settings", "privacySettings": "Privacy Settings", "accessibilitySettings": "Accessibility Settings", "languageSettings": "Language Settings", "displaySettings": "Display Settings", "soundSettings": "Sound Settings", "dataSettings": "Data Settings", "syncSettings": "Sync Settings", "backupSettings": "Backup Settings", "advancedSettings": "Advanced Settings", "developerSettings": "Developer Settings", "experimentalFeatures": "Experimental Features", "resetSettings": "Reset Settings", "resetToDefault": "Reset to De<PERSON>ult", "saveSettings": "Save Settings", "settingsSaved": "Settings Saved", "settingsNotSaved": "Settings Not Saved", "autoSave": "Auto Save", "autoSaveEnabled": "Auto Save Enabled", "autoSaveDisabled": "Auto Save Disabled", "lastSaved": "Last Saved", "changeEmail": "Change your email address", "changePassword": "Change your password", "receiveEmails": "Receive emails about your account", "reminderDesc": "Get notified before your appointments"}, "social": {"social": "Social", "feed": "Social Feed", "friends": "Friends", "messages": "Messages", "post": "Post", "posts": "Posts", "posting": "Posting...", "comment": "Comment", "comments": "Comments", "viewComments": "View Comments", "viewAllComments": "View all {{count}} comments", "beFirstToComment": "Be the first to comment", "noComments": "No comments yet", "like": "Like", "likes": "<PERSON>s", "share": "Share", "shareVia": "Share via", "copied": "Copied!", "copyLink": "Copy Link", "channels": "Channels", "channel": "Channel", "yourChannels": "Your Channels", "joinChannel": "Join Channel", "members": "Members", "member": "Member", "joinedOn": "Joined on", "typeMessage": "Type a message...", "noMessagesYet": "No messages yet", "beFirstToMessage": "Be the first to send a message in this channel", "channelNotFound": "Channel not found", "channelNotFoundDescription": "The channel you're looking for doesn't exist or has been removed", "trendingTopics": "Trending Topics", "peopleYouMayKnow": "People You May Know", "mutualFriends": "mutual friends", "sponsored": "Sponsored", "learnMore": "Learn More", "hashtags": "Hashtags", "addHashtags": "Add hashtags...", "hashtagsRequired": "At least one hashtag is required for each post", "postAnonymously": "Post anonymously", "anonymous": "Anonymous", "edited": "edited", "whatsOnYourMind": "What's on your mind?", "createPost": "Create Post", "createPostPlaceholder": "Share something with your network...", "edit": "Edit", "delete": "Delete", "searchUsers": "Search users", "noConversations": "No conversations yet", "selectConversation": "Select a conversation to start messaging", "noMessages": "No messages yet", "startConversation": "Start a conversation", "sendFirstMessage": "Type your first message...", "pendingFriendRequest": "Friend request pending", "messageLimit": "You can only send 2 messages until the friend request is accepted", "sendFriendRequest": "Send Friend Request", "orSendMessage": "Or send a message to automatically create a friend request", "friendRequestSent": "Friend request sent", "friendRequestAccepted": "Friend request accepted", "friendRequestRejected": "Friend request rejected", "fitness": "Fitness", "food": "Food", "anatomy": "Anatomy", "general": "General", "confirmDeletePost": "Are you sure you want to delete this post?", "following": "Following", "follow": "Follow", "relatedHashtags": "Related Hashtags", "recent": "Recent", "trending": "Trending", "top": "Top", "loadingPosts": "Loading posts...", "noPostsForHashtag": "No posts found for #{tag}", "beFirstToPostHashtag": "Be the first to post with #{tag}", "backToFeed": "Back to Feed", "errorLoadingHashtagPosts": "Error loading posts for this hashtag", "hashtagNotFound": "Hashtag not found", "newPostsAvailable": "New posts available", "addImage": "Add Image", "addEmoji": "Add <PERSON>", "mentionUser": "Mention User", "addHashtag": "Add <PERSON>htag", "addLocation": "Add Location", "enterLocation": "Enter your location", "expand": "Expand", "emptyPostError": "Post cannot be empty", "errorCreatingPost": "Error creating post", "postCreated": "Post created successfully", "public": "Public", "friendsOnly": "Friends Only", "onlyMe": "Only Me", "writeComment": "Write a comment...", "popularHashtag": "Popular hashtag", "selectConversationDesc": "Choose a conversation from the list or start a new one", "noPosts": "No posts yet", "peopleMayKnow": "People you may know", "seeAll": "See all", "noSuggestions": "No suggestions available", "imageTooLarge": "Image is too large (max 5MB)", "invalidImageType": "Invalid image type", "emptyPost": "Post cannot be empty", "errorLoadingData": "Error loading data", "errorLoadingComments": "Error loading comments", "errorCreatingComment": "Error creating comment", "refresh": "Refresh", "manageFriends": "Manage Friends", "friendRequests": "Friend Requests", "pendingRequest": "Pending Request", "addFriend": "Add Friend", "removeFriend": "Remove <PERSON>", "message": "Message", "accept": "Accept", "reject": "Reject", "confirmRemoveFriend": "Are you sure you want to remove this friend?", "allFriends": "All Friends", "suggestions": "Suggestions", "requests": "Requests", "sent": "<PERSON><PERSON>", "requestSent": "Request Sent", "requestSentTo": "Request sent to {{name}}", "cancel": "Cancel", "searching": "Searching...", "noSearchResults": "No users found", "noFriends": "You don't have any friends yet", "noFriendRequests": "No friend requests", "noSentRequests": "No sent requests", "noResults": "No results", "searchResults": "Search Results", "errorLoadingConversations": "Error loading conversations", "errorLoadingMessages": "Error loading messages", "instagramShareInfo": "Instagram doesn't support direct sharing. Please take a screenshot and share it on Instagram."}, "errors": {"somethingWentWrong": "Something went wrong", "tryAgain": "Please try again", "pageNotFound": "Page not found", "returnHome": "Return to home", "serverError": "Server error", "connectionError": "Connection error", "checkConnection": "Please check your internet connection", "unauthorized": "Unauthorized", "pleaseLogin": "Please login to continue", "sessionExpired": "Your session has expired", "loginAgain": "Please login again", "formError": "Please fix the errors in the form", "requiredField": "This field is required", "invalidEmail": "Invalid email address", "invalidPassword": "Password must be at least 8 characters", "invalidPhone": "Invalid phone number", "invalidDate": "Invalid date", "fileTooBig": "File is too big", "unsupportedFileType": "Unsupported file type"}, "onboarding": {"welcome": "Welcome to CareAI", "getStarted": "Let's get started", "personalInfo": "Personal Information", "medicalInfo": "Medical Information", "appTour": "App Tour", "allSet": "You're all set!", "skip": "<PERSON><PERSON>", "skipTour": "Skip Tour", "startTour": "Start Tour", "completeSetup": "Complete Setup", "personalInfoDesc": "Let's get to know you better", "medicalInfoDesc": "This helps us provide better recommendations", "appTourDesc": "Let's explore the key features", "allSetDesc": "You're ready to start using CareAI", "dashboard": "Dashboard", "dashboardDesc": "Get a quick overview of your health", "predictions": "Predictions", "predictionsDesc": "AI-powered health risk assessments", "tracking": "Health Tracking", "trackingDesc": "Monitor your daily health metrics", "appointments": "Appointments", "appointmentsDesc": "Schedule and manage doctor visits", "chatbot": "Medical Chatbot", "chatbotDesc": "Get instant answers to health questions"}, "notifications": {"title": "Notifications", "markAsRead": "<PERSON> <PERSON>", "markAllAsRead": "<PERSON> as <PERSON>", "delete": "Delete", "clearAll": "Clear All", "noNotifications": "No notifications", "viewAll": "View All", "settings": "Notification Settings", "enablePush": "Enable Push Notifications", "enableEmail": "Enable Email Notifications", "enableSMS": "Enable SMS Notifications", "pushNotifications": "Push Notifications", "emailNotifications": "Email Notifications", "smsNotifications": "SMS Notifications", "appointmentReminders": "Appointment Reminders", "medicationReminders": "Medication Reminders", "healthTips": "Health Tips", "systemUpdates": "System Updates", "newFeatures": "New Features", "accountAlerts": "Account <PERSON><PERSON>", "predictionResults": "Prediction Results", "dailyReminders": "Daily Reminders", "weeklyReports": "Weekly Reports", "monthlyReports": "Monthly Reports", "notificationFrequency": "Notification Frequency", "immediate": "Immediate", "daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "never": "Never", "saveSettings": "Save Settings", "settingsSaved": "Notification settings saved successfully", "view": "View Notifications", "likeNotification": "{{name}} liked your post", "commentNotification": "{{name}} commented on your post", "mentionNotification": "{{name}} mentioned you in a comment", "friendRequestNotification": "{{name}} sent you a friend request", "friendRequestAcceptedNotification": "{{name}} accepted your friend request", "messageNotification": "{{name}} sent you a message"}, "weather": {"title": "Weather Forecast", "currentWeather": "Current Weather", "forecast": "Forecast", "hourlyForecast": "Hourly Forecast", "dailyForecast": "Daily Forecast", "temperature": "Temperature", "feelsLike": "Feels Like", "wind": "Wind", "humidity": "<PERSON><PERSON><PERSON><PERSON>", "precipitation": "Precipitation", "pressure": "Pressure", "uvIndex": "UV Index", "sunrise": "Sunrise", "sunset": "Sunset", "highTemp": "High", "lowTemp": "Low", "chanceOfRain": "Chance of Rain", "searchLocation": "Search location", "today": "Today", "tomorrow": "Tomorrow", "weatherTips": "Weather-Based Health Tips", "highUV": "High UV Index", "highHumidity": "High Humidity", "coldTemp": "Cold Temperature", "rainyConditions": "Rainy <PERSON>", "favorableWeather": "Favorable Weather"}, "healthNews": {"title": "Health News", "refresh": "Refresh", "poweredBy": "Powered by News API", "autoRefresh": "Auto-refreshes in", "usingMockData": "Sample data", "new": "New!"}}