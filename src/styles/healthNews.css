/* Health News Styles */

.health-news-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

/* News Controls */
.news-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.news-search {
  flex: 1;
  min-width: 250px;
}

.search-input-container {
  position: relative;
  display: flex;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem;
  padding-right: 3rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  background-color: white;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.search-button {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
}

.search-button:hover {
  color: #3b82f6;
  background-color: rgba(59, 130, 246, 0.1);
}

.news-actions {
  display: flex;
  gap: 0.75rem;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.625rem 1rem;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-button:hover {
  background-color: #f9fafb;
  border-color: #d1d5db;
}

.action-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

/* News Filter */
.news-filter {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  margin-bottom: 1.5rem;
  overflow: hidden;
}

.filter-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.filter-header h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.category-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  padding: 1.25rem 1.5rem;
}

.category-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.625rem 1rem;
  background-color: #f3f4f6;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  cursor: pointer;
  transition: all 0.2s ease;
}

.category-button:hover {
  background-color: #e5e7eb;
}

.category-button.active {
  background-color: #3b82f6;
  color: white;
}

.category-icon {
  font-size: 1.25rem;
}

/* Last Updated */
.last-updated {
  font-size: 0.75rem;
  color: #6b7280;
  margin-bottom: 1rem;
  text-align: right;
}

/* Mock Data Warning */
.mock-data-warning {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background-color: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 0.5rem;
  color: #92400e;
  margin-bottom: 1.5rem;
  font-size: 0.875rem;
}

@media (prefers-color-scheme: dark) {
  .mock-data-warning {
    background-color: rgba(245, 158, 11, 0.2);
    border-color: #f59e0b;
    color: #fbbf24;
  }
}

/* News Grid */
.news-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

/* News Card */
.news-card {
  background-color: white;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.news-card-image {
  position: relative;
  height: 180px;
  overflow: hidden;
}

.news-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.news-card:hover .news-card-image img {
  transform: scale(1.05);
}

.news-source {
  position: absolute;
  bottom: 0;
  left: 0;
  background-color: rgba(59, 130, 246, 0.9);
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.75rem;
  border-top-right-radius: 0.5rem;
}

.news-card-content {
  padding: 1.25rem;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.news-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.75rem 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-description {
  font-size: 0.875rem;
  color: #4b5563;
  margin: 0 0 1rem 0;
  line-height: 1.5;
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  font-size: 0.75rem;
  color: #6b7280;
}

.news-date, .news-author {
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

.news-author {
  max-width: 50%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.news-read-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.625rem 1rem;
  background-color: #3b82f6;
  color: white;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.2s ease;
  margin-top: auto;
}

.news-read-more:hover {
  background-color: #2563eb;
}

/* Loading State */
.news-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 0;
  color: #6b7280;
}

.loading-spinner {
  width: 2.5rem;
  height: 2.5rem;
  border: 3px solid #e5e7eb;
  border-top-color: #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Error State */
.news-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 0;
  color: #ef4444;
  text-align: center;
}

.news-error p {
  margin: 1rem 0;
}

.retry-button {
  padding: 0.625rem 1.25rem;
  background-color: #ef4444;
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retry-button:hover {
  background-color: #dc2626;
}

/* No Results */
.no-news {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3rem 0;
  color: #6b7280;
}

/* Dark Mode */
@media (prefers-color-scheme: dark) {
  .search-input {
    background-color: #1f2937;
    border-color: #374151;
    color: #f3f4f6;
  }

  .search-input:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
  }

  .search-button {
    color: #9ca3af;
  }

  .search-button:hover {
    color: #3b82f6;
    background-color: rgba(59, 130, 246, 0.2);
  }

  .action-button {
    background-color: #1f2937;
    border-color: #374151;
    color: #d1d5db;
  }

  .action-button:hover {
    background-color: #374151;
    border-color: #4b5563;
  }

  .news-filter {
    background-color: #1f2937;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.1);
  }

  .filter-header {
    border-bottom-color: #374151;
  }

  .filter-header h3 {
    color: #f3f4f6;
  }

  .category-button {
    background-color: #374151;
    color: #d1d5db;
  }

  .category-button:hover {
    background-color: #4b5563;
  }

  .category-button.active {
    background-color: #3b82f6;
    color: white;
  }

  .last-updated {
    color: #9ca3af;
  }

  .news-card {
    background-color: #1f2937;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.1);
  }

  .news-title {
    color: #f3f4f6;
  }

  .news-description {
    color: #d1d5db;
  }

  .news-meta {
    color: #9ca3af;
  }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .news-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .news-actions {
    justify-content: space-between;
  }

  .news-grid {
    grid-template-columns: 1fr;
  }

  .news-card-image {
    height: 200px;
  }
}
