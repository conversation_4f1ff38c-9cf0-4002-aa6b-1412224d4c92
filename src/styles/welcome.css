/* Welcome Page Styles - Spotify Theme Enforced */

/* Base styles */
.welcome-page {
  min-height: 100vh;
  width: 100%;
  overflow-x: hidden;
  position: relative;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-attachment: fixed;
  /* Force Spotify dark theme */
  background-color: #121212 !important;
  color: #ffffff !important;
}

/* Force dark theme for welcome page regardless of class */
.welcome-page,
.welcome-page.light,
.welcome-page.dark {
  background-color: #121212 !important;
  color: #ffffff !important;
}

/* Theme-specific styles */
.welcome-page.light {
  /* Light theme with Spotify accent colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --text-primary: #1f2937;
  --text-secondary: #4b5563;
  --accent-primary: #1db954;
  --accent-secondary: #1ed760;
  --accent-tertiary: #1aa34a;
  --accent-quaternary: #1db954;
  --card-bg: #ffffff;
  --card-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  --gradient-primary: linear-gradient(135deg, #1db954 0%, #1ed760 100%);
  --gradient-secondary: linear-gradient(135deg, #1aa34a 0%, #1db954 100%);
  --gradient-tertiary: linear-gradient(135deg, #1db954 0%, #1ed760 100%);
  --btn-text: #ffffff;
  --btn-secondary-bg: #f3f4f6;
  --btn-secondary-text: #1f2937;
  --btn-outline-border: #d1d5db;
  --circle-opacity: 0.15;
  --glass-bg: rgba(255, 255, 255, 0.7);
  --glass-border: rgba(255, 255, 255, 0.5);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  --pattern-opacity: 0.03;
}

.welcome-page.dark {
  /* Spotify Dark Theme Colors */
  --bg-primary: #121212;
  --bg-secondary: #181818;
  --text-primary: #ffffff;
  --text-secondary: #b3b3b3;
  --accent-primary: #1db954;
  --accent-secondary: #1ed760;
  --accent-tertiary: #1aa34a;
  --accent-quaternary: #1db954;
  --card-bg: #181818;
  --card-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
  --gradient-primary: linear-gradient(135deg, #1db954 0%, #1ed760 100%);
  --gradient-secondary: linear-gradient(135deg, #1aa34a 0%, #1db954 100%);
  --gradient-tertiary: linear-gradient(135deg, #1db954 0%, #1ed760 100%);
  --btn-text: #000000;
  --btn-secondary-bg: #282828;
  --btn-secondary-text: #ffffff;
  --btn-outline-border: #333333;
  --circle-opacity: 0.15;
  --glass-bg: rgba(18, 18, 18, 0.8);
  --glass-border: rgba(40, 40, 40, 0.5);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  --pattern-opacity: 0.03;
}

/* Background elements */
.welcome-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  overflow: hidden;
}

.welcome-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 20% 30%, rgba(58, 134, 255, 0.15) 0%, transparent 25%),
    radial-gradient(circle at 80% 70%, rgba(255, 0, 110, 0.15) 0%, transparent 25%);
  opacity: var(--pattern-opacity);
  z-index: -1;
}

.circle {
  position: absolute;
  border-radius: 50%;
  opacity: var(--circle-opacity);
  mix-blend-mode: normal;
}

.circle-1 {
  width: 90vw;
  height: 90vw;
  background: var(--gradient-primary);
  top: -45vw;
  right: -45vw;
  filter: blur(80px);
  animation: pulse-slow 15s infinite alternate;
}

.circle-2 {
  width: 70vw;
  height: 70vw;
  background: var(--gradient-secondary);
  bottom: -35vw;
  left: -35vw;
  filter: blur(80px);
  animation: pulse-slow 20s infinite alternate-reverse;
}

.circle-3 {
  width: 50vw;
  height: 50vw;
  background: var(--gradient-tertiary);
  top: 40%;
  left: 60%;
  filter: blur(80px);
  animation: pulse-slow 18s infinite alternate;
}

@keyframes pulse-slow {
  0% {
    transform: scale(1);
    opacity: var(--circle-opacity);
  }
  50% {
    transform: scale(1.1);
    opacity: calc(var(--circle-opacity) * 0.8);
  }
  100% {
    transform: scale(1);
    opacity: var(--circle-opacity);
  }
}

/* Header */
.welcome-header {
  padding: 1.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 10;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.app-logo {
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-animation {
  filter: drop-shadow(0 0 8px rgba(29, 185, 84, 0.5));
  animation: logo-glow 3s infinite alternate;
  transition: transform 0.3s ease;
}

.logo-animation:hover {
  transform: scale(1.05);
}

@keyframes logo-glow {
  0% {
    filter: drop-shadow(0 0 5px rgba(29, 185, 84, 0.5));
  }
  100% {
    filter: drop-shadow(0 0 15px rgba(29, 185, 84, 0.8));
  }
}

.logo-container h1 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

/* Hero Section */
.hero-section {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

.hero-content {
  max-width: 800px;
  text-align: center;
  z-index: 2;
  margin-top: 4rem;
}

.hero-badge {
  display: inline-block;
  background: var(--glass-bg);
  backdrop-filter: blur(8px);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  padding: 0.5rem 1.25rem;
  border-radius: 9999px;
  margin-bottom: 1.5rem;
  font-weight: 600;
  font-size: 0.875rem;
  color: var(--accent-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  line-height: 1.2;
}

.gradient-text {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: var(--text-secondary);
  margin-bottom: 2.5rem;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 2rem;
}

.hero-features {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  justify-content: center;
  margin-bottom: 2.5rem;
}

.feature-pill {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--glass-bg);
  backdrop-filter: blur(8px);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  padding: 0.5rem 1rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  color: var(--text-primary);
  transition: all 0.3s ease;
}

.feature-pill svg {
  color: var(--accent-primary);
}

.feature-pill:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.btn {
  padding: 0.875rem 2.5rem;
  border-radius: 9999px;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(-100%) rotate(45deg);
  transition: transform 0.6s ease;
  z-index: -1;
}

.btn:hover::before {
  transform: translateX(100%) rotate(45deg);
}

.btn-primary {
  background: var(--gradient-primary);
  color: var(--btn-text);
  box-shadow: 0 4px 14px rgba(58, 134, 255, 0.4);
}

.btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(58, 134, 255, 0.5);
}

.btn-secondary {
  background: var(--glass-bg);
  backdrop-filter: blur(8px);
  border: 1px solid var(--glass-border);
  color: var(--text-primary);
}

.btn-secondary:hover {
  transform: translateY(-3px);
  box-shadow: var(--glass-shadow);
}

.btn-outline {
  background: transparent;
  border: 2px solid var(--btn-outline-border);
  color: var(--text-primary);
}

.btn-outline:hover {
  background: var(--btn-secondary-bg);
  transform: translateY(-3px);
}

.hero-trust {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  justify-content: center;
  margin-top: 2rem;
}

.trust-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
}

.trust-badge svg {
  color: var(--accent-primary);
}

.hero-devices {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
}

.device {
  position: absolute;
  filter: drop-shadow(0 20px 30px rgba(0, 0, 0, 0.2));
}

.device-phone {
  width: 200px;
  right: 10%;
  top: 40%;
  z-index: 3;
}

.device-tablet {
  width: 300px;
  right: 25%;
  top: 55%;
  z-index: 2;
}

.device-laptop {
  width: 400px;
  right: 40%;
  top: 60%;
  z-index: 1;
}

.device-image {
  width: 100%;
  height: auto;
  object-fit: contain;
}

/* Device placeholders */
.device-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 12px;
  background: var(--glass-bg);
  backdrop-filter: blur(8px);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease;
}

.device-placeholder::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  z-index: 1;
}

.phone-placeholder {
  width: 180px;
  height: 360px;
  border-radius: 36px;
  transform: rotate(-5deg);
}

.tablet-placeholder {
  width: 280px;
  height: 400px;
  border-radius: 24px;
}

.laptop-placeholder {
  width: 400px;
  height: 250px;
  border-radius: 12px;
  transform: rotate(5deg);
}

.device-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 2;
}

.device-header {
  height: 24px;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--btn-outline-border);
  display: flex;
  align-items: center;
  justify-content: center;
}

.device-header::before {
  content: '';
  width: 40px;
  height: 4px;
  background-color: var(--btn-outline-border);
  border-radius: 2px;
}

.device-screen {
  flex: 1;
  padding: 12px;
  display: flex;
  flex-direction: column;
  background-image:
    radial-gradient(circle at 10% 20%, rgba(58, 134, 255, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 90% 80%, rgba(255, 0, 110, 0.05) 0%, transparent 50%);
}

.tablet-placeholder .device-screen,
.laptop-placeholder .device-screen {
  flex-direction: row;
}

.app-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: var(--gradient-primary);
  margin: 0 auto 16px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  animation: pulse 2s infinite alternate;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.05);
  }
}

.app-text {
  height: 12px;
  background-color: var(--btn-outline-border);
  border-radius: 6px;
  margin-bottom: 12px;
  width: 80%;
  margin-left: auto;
  margin-right: auto;
  opacity: 0.7;
}

.app-button {
  height: 36px;
  background: var(--gradient-primary);
  border-radius: 18px;
  width: 70%;
  margin: 24px auto 0;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.app-sidebar {
  width: 20%;
  background-color: var(--bg-secondary);
  border-right: 1px solid var(--btn-outline-border);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 20px;
  gap: 15px;
}

.app-sidebar::before,
.app-sidebar::after {
  content: '';
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--gradient-primary);
  opacity: 0.7;
}

.app-main {
  flex: 1;
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.app-card {
  height: 80px;
  background-color: var(--glass-bg);
  border-radius: 8px;
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  position: relative;
  overflow: hidden;
}

.app-card::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background: var(--gradient-primary);
  border-radius: 2px 0 0 2px;
}

.app-chart {
  height: 120px;
  background: linear-gradient(to right, rgba(58, 134, 255, 0.2) 0%, transparent 100%);
  border-radius: 8px;
  position: relative;
  overflow: hidden;
  border: 1px solid var(--glass-border);
}

.app-chart::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    linear-gradient(90deg, transparent 0%, transparent 50%, rgba(255, 255, 255, 0.05) 50%, rgba(255, 255, 255, 0.05) 100%) 0 0 / 20px 100%,
    linear-gradient(0deg, transparent 0%, transparent 50%, rgba(255, 255, 255, 0.05) 50%, rgba(255, 255, 255, 0.05) 100%) 0 0 / 100% 20px;
}

.app-chart::after {
  content: "";
  position: absolute;
  top: 30%;
  left: 0;
  width: 100%;
  height: 40%;
  background: var(--accent-primary);
  clip-path: polygon(0 50%, 10% 40%, 20% 60%, 30% 30%, 40% 40%, 50% 20%, 60% 40%, 70% 30%, 80% 50%, 90% 40%, 100% 60%, 100% 100%, 0 100%);
  opacity: 0.3;
  animation: wave 5s infinite linear;
}

@keyframes wave {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.app-data {
  height: 40px;
  background-color: var(--glass-bg);
  border-radius: 8px;
  border: 1px solid var(--glass-border);
  display: flex;
  align-items: center;
  padding: 0 12px;
}

.app-data::before {
  content: '';
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--accent-tertiary);
  margin-right: 8px;
  animation: blink 2s infinite alternate;
}

@keyframes blink {
  0% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.scroll-indicator {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
  cursor: pointer;
  z-index: 10;
}

.scroll-indicator p {
  font-size: 0.75rem;
  letter-spacing: 0.1em;
  margin: 0;
}

/* Features Section */
.features-section {
  padding: 8rem 2rem;
  background-color: var(--bg-secondary);
  position: relative;
  overflow: hidden;
}

.features-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 10% 10%, rgba(58, 134, 255, 0.05) 0%, transparent 30%),
    radial-gradient(circle at 90% 90%, rgba(255, 0, 110, 0.05) 0%, transparent 30%);
  opacity: var(--pattern-opacity);
  z-index: 0;
}

.section-header {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 5rem;
  position: relative;
  z-index: 1;
}

.section-badge {
  display: inline-block;
  background: var(--glass-bg);
  backdrop-filter: blur(8px);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  padding: 0.5rem 1.25rem;
  border-radius: 9999px;
  margin-bottom: 1.5rem;
  font-weight: 600;
  font-size: 0.875rem;
  color: var(--accent-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.section-title {
  font-size: 2.75rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  line-height: 1.2;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

.section-subtitle {
  font-size: 1.25rem;
  color: var(--text-secondary);
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.6;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2.5rem;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.feature-card {
  background-color: var(--glass-bg);
  backdrop-filter: blur(8px);
  border: 1px solid var(--glass-border);
  border-radius: 1.5rem;
  padding: 2.5rem;
  box-shadow: var(--glass-shadow);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  text-align: left;
  position: relative;
  overflow: hidden;
}

.feature-card.featured {
  border: 1px solid rgba(58, 134, 255, 0.3);
  background: linear-gradient(135deg, var(--glass-bg), rgba(58, 134, 255, 0.05));
}

.feature-badge {
  position: absolute;
  top: 1.25rem;
  right: 1.25rem;
  background: var(--gradient-primary);
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 30px rgba(0, 0, 0, 0.15);
}

.feature-icon-wrapper {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  background: var(--glass-bg);
  backdrop-filter: blur(8px);
  border: 1px solid var(--glass-border);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  position: relative;
  overflow: hidden;
}

.feature-icon-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(58, 134, 255, 0.2), rgba(255, 0, 110, 0.2));
  opacity: 0.5;
}

.feature-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-primary);
  position: relative;
  z-index: 1;
}

.feature-card h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.feature-card p {
  color: var(--text-secondary);
  line-height: 1.7;
  margin-bottom: 1.5rem;
  flex-grow: 1;
}

.feature-footer {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: auto;
}

.feature-tag {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  color: var(--accent-primary);
}

/* CTA Section */
.cta-section {
  padding: 8rem 2rem;
  background: var(--bg-primary);
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.cta-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.cta-shape {
  position: absolute;
  border-radius: 50%;
  filter: blur(80px);
  opacity: 0.15;
}

.cta-shape-1 {
  width: 40vw;
  height: 40vw;
  background: var(--gradient-primary);
  top: -20vw;
  right: -10vw;
}

.cta-shape-2 {
  width: 30vw;
  height: 30vw;
  background: var(--gradient-secondary);
  bottom: -15vw;
  left: -10vw;
}

.cta-content {
  max-width: 700px;
  position: relative;
  z-index: 1;
  background: var(--glass-bg);
  backdrop-filter: blur(8px);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  border-radius: 2rem;
  padding: 3rem;
}

.cta-badge {
  display: inline-block;
  background: var(--gradient-primary);
  color: white;
  padding: 0.5rem 1.25rem;
  border-radius: 9999px;
  margin-bottom: 1.5rem;
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.cta-content h2 {
  font-size: 2.75rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  line-height: 1.2;
}

.cta-content p {
  font-size: 1.25rem;
  color: var(--text-secondary);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.cta-features {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 2.5rem;
}

.cta-feature {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  color: var(--text-primary);
}

.cta-feature svg {
  color: var(--accent-primary);
}

.cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

/* Footer */
.welcome-footer {
  background-color: var(--bg-secondary);
  padding: 6rem 2rem 2rem;
  color: var(--text-secondary);
  position: relative;
  overflow: hidden;
}

.footer-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(58, 134, 255, 0.05) 0%, transparent 25%),
    radial-gradient(circle at 80% 20%, rgba(255, 0, 110, 0.05) 0%, transparent 25%);
  opacity: var(--pattern-opacity);
  z-index: 0;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 4rem;
  position: relative;
  z-index: 1;
}

.footer-branding {
  flex: 1;
  min-width: 250px;
  max-width: 350px;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.footer-logo h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.footer-tagline {
  color: var(--text-secondary);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.footer-social {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--glass-bg);
  backdrop-filter: blur(8px);
  border: 1px solid var(--glass-border);
  color: var(--text-primary);
  transition: all 0.3s ease;
}

.social-link:hover {
  transform: translateY(-3px);
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--glass-shadow);
}

.footer-links {
  display: flex;
  flex-wrap: wrap;
  gap: 3rem;
  flex: 2;
}

.footer-column {
  min-width: 160px;
}

.footer-column h4 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  position: relative;
  display: inline-block;
}

.footer-column h4::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 30px;
  height: 2px;
  background: var(--gradient-primary);
  border-radius: 1px;
}

.footer-column ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-column li {
  margin-bottom: 0.75rem;
}

.footer-column a {
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.2s ease;
  position: relative;
  display: inline-block;
}

.footer-column a::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 1px;
  background: var(--gradient-primary);
  transition: width 0.3s ease;
}

.footer-column a:hover {
  color: var(--accent-primary);
}

.footer-column a:hover::after {
  width: 100%;
}

.footer-bottom {
  max-width: 1200px;
  margin: 4rem auto 0;
  padding-top: 1.5rem;
  border-top: 1px solid var(--btn-outline-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
  font-size: 0.875rem;
  position: relative;
  z-index: 1;
}

.footer-bottom-links {
  display: flex;
  gap: 1.5rem;
}

.footer-bottom-links a {
  color: var(--text-secondary);
  text-decoration: none;
  transition: color 0.2s ease;
}

.footer-bottom-links a:hover {
  color: var(--accent-primary);
}

/* Responsive styles */
@media (max-width: 1200px) {
  .hero-title {
    font-size: 3rem;
  }

  .device-phone {
    width: 150px;
    right: 5%;
  }

  .device-tablet {
    width: 220px;
    right: 20%;
  }

  .device-laptop {
    width: 300px;
    right: 35%;
  }

  .section-title {
    font-size: 2.25rem;
  }

  .cta-content {
    max-width: 600px;
    padding: 2.5rem;
  }

  .cta-content h2 {
    font-size: 2.25rem;
  }
}

@media (max-width: 992px) {
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .footer-branding {
    max-width: 100%;
    margin-bottom: 1rem;
  }

  .footer-links {
    width: 100%;
    justify-content: space-between;
  }
}

@media (max-width: 768px) {
  .hero-badge {
    font-size: 0.75rem;
  }

  .hero-title {
    font-size: 2.25rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .hero-features {
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
  }

  .hero-buttons {
    flex-direction: column;
    width: 100%;
    max-width: 300px;
    margin-left: auto;
    margin-right: auto;
  }

  .device {
    opacity: 0.5 !important;
  }

  .device-phone {
    width: 120px;
    right: 5%;
    top: 30%;
  }

  .device-tablet {
    width: 180px;
    right: 15%;
    top: 45%;
  }

  .device-laptop {
    width: 240px;
    right: 25%;
    top: 60%;
  }

  .section-title {
    font-size: 2rem;
  }

  .section-subtitle {
    font-size: 1rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .feature-card {
    padding: 2rem;
  }

  .cta-content {
    padding: 2rem;
  }

  .cta-content h2 {
    font-size: 2rem;
  }

  .cta-content p {
    font-size: 1rem;
  }

  .cta-features {
    flex-direction: column;
    align-items: center;
  }

  .footer-content {
    flex-direction: column;
    gap: 2rem;
  }

  .footer-links {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }

  .footer-bottom {
    flex-direction: column;
    text-align: center;
  }
}

@media (max-width: 576px) {
  .welcome-header {
    padding: 1rem;
  }

  .hero-section {
    padding: 1rem;
  }

  .hero-badge {
    margin-top: 3rem;
  }

  .hero-title {
    font-size: 1.75rem;
  }

  .hero-subtitle {
    font-size: 0.875rem;
  }

  .hero-trust {
    flex-direction: column;
    gap: 1rem;
  }

  .section-badge {
    font-size: 0.75rem;
  }

  .section-title {
    font-size: 1.75rem;
  }

  .feature-card {
    padding: 1.5rem;
  }

  .feature-card h3 {
    font-size: 1.25rem;
  }

  .device-tablet, .device-laptop {
    display: none;
  }

  .device-phone {
    width: 150px;
    right: 50%;
    transform: translateX(50%);
    top: 65%;
  }

  .cta-content {
    padding: 1.5rem;
  }

  .cta-content h2 {
    font-size: 1.75rem;
  }

  .cta-buttons {
    flex-direction: column;
    width: 100%;
  }

  .footer-links {
    grid-template-columns: 1fr;
  }

  .footer-column {
    margin-bottom: 1.5rem;
  }

  .footer-bottom-links {
    flex-direction: column;
    gap: 0.5rem;
  }
}
