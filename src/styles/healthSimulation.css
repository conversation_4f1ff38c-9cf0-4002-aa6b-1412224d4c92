/* Health Simulation Styles */
.simulation-container {
  background-color: var(--background);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.simulation-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--foreground);
  margin-bottom: 1.5rem;
  text-align: center;
}

/* Simulation Controls */
.simulation-controls {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 1.5rem;
  margin-bottom: 2rem;
  background-color: var(--card);
  border-radius: 0.75rem;
  padding: 1.5rem;
  border: 1px solid var(--border);
}

@media (min-width: 768px) {
  .simulation-controls {
    grid-template-columns: repeat(2, 1fr);
  }
}

.simulation-control {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.simulation-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--foreground);
}

.simulation-slider {
  -webkit-appearance: none;
  width: 100%;
  height: 0.5rem;
  border-radius: 0.25rem;
  background: var(--secondary);
  outline: none;
  transition: all 0.2s;
}

.simulation-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  border-radius: 50%;
  background: var(--primary);
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.simulation-slider::-moz-range-thumb {
  width: 1.25rem;
  height: 1.25rem;
  border-radius: 50%;
  background: var(--primary);
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.simulation-slider::-webkit-slider-thumb:hover {
  background: var(--primary-hover);
  transform: scale(1.1);
}

.simulation-slider::-moz-range-thumb:hover {
  background: var(--primary-hover);
  transform: scale(1.1);
}

.simulation-value {
  font-size: 0.875rem;
  color: var(--muted-foreground);
  text-align: right;
}

/* Simulation Results */
.simulation-results {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
}

@media (min-width: 640px) {
  .simulation-results {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .simulation-results {
    grid-template-columns: repeat(3, 1fr);
  }
}

.result-card {
  background-color: var(--card);
  border-radius: 0.75rem;
  padding: 1rem;
  border: 1px solid var(--border);
  transition: all 0.2s;
}

.result-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.result-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--foreground);
}

.result-change {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}

.result-change.positive {
  background-color: rgba(16, 185, 129, 0.1);
  color: rgb(16, 185, 129);
}

.result-change.negative {
  background-color: rgba(239, 68, 68, 0.1);
  color: rgb(239, 68, 68);
}

.result-change.neutral {
  background-color: rgba(107, 114, 128, 0.1);
  color: rgb(107, 114, 128);
}

.result-values {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.result-current, .result-projected {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.result-label {
  font-size: 0.75rem;
  color: var(--muted-foreground);
  margin-bottom: 0.25rem;
}

.result-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--foreground);
}

.result-arrow {
  color: var(--muted-foreground);
}

/* Timeline */
.timeline-container {
  background-color: var(--card);
  border-radius: 0.75rem;
  padding: 1.5rem;
  border: 1px solid var(--border);
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.timeline-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--foreground);
}

.timeline-legend {
  display: flex;
  gap: 1rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: var(--muted-foreground);
}

.legend-color {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
}

.timeline-chart {
  height: 250px;
}

/* Recommendations */
.recommendations {
  margin-top: 2rem;
  background-color: var(--card);
  border-radius: 0.75rem;
  padding: 1.5rem;
  border: 1px solid var(--border);
}

.recommendations-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--foreground);
  margin-bottom: 1rem;
}

.recommendation-list {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 1rem;
}

@media (min-width: 768px) {
  .recommendation-list {
    grid-template-columns: repeat(2, 1fr);
  }
}

.recommendation-item {
  display: flex;
  gap: 0.75rem;
  padding: 1rem;
  background-color: var(--background);
  border-radius: 0.5rem;
  border: 1px solid var(--border);
}

.recommendation-icon {
  width: 2rem;
  height: 2rem;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.recommendation-icon.exercise {
  background-color: rgba(16, 185, 129, 0.1);
  color: rgb(16, 185, 129);
}

.recommendation-icon.diet {
  background-color: rgba(245, 158, 11, 0.1);
  color: rgb(245, 158, 11);
}

.recommendation-icon.sleep {
  background-color: rgba(139, 92, 246, 0.1);
  color: rgb(139, 92, 246);
}

.recommendation-icon.stress {
  background-color: rgba(59, 130, 246, 0.1);
  color: rgb(59, 130, 246);
}

.recommendation-content {
  flex: 1;
}

.recommendation-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--foreground);
  margin-bottom: 0.25rem;
}

.recommendation-description {
  font-size: 0.75rem;
  color: var(--muted-foreground);
}

/* Dark mode variables */
:root {
  --background: #ffffff;
  --foreground: #1f2937;
  --card: #f9fafb;
  --card-foreground: #1f2937;
  --primary: #3b82f6;
  --primary-foreground: #ffffff;
  --primary-hover: #2563eb;
  --secondary: #f3f4f6;
  --secondary-foreground: #1f2937;
  --muted: #f3f4f6;
  --muted-foreground: #6b7280;
  --accent: #f3f4f6;
  --accent-foreground: #1f2937;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #e5e7eb;
  --input: #e5e7eb;
  --ring: #3b82f6;
}

.dark {
  --background: #1f2937;
  --foreground: #f9fafb;
  --card: #111827;
  --card-foreground: #f9fafb;
  --primary: #3b82f6;
  --primary-foreground: #ffffff;
  --primary-hover: #2563eb;
  --secondary: #374151;
  --secondary-foreground: #f9fafb;
  --muted: #374151;
  --muted-foreground: #9ca3af;
  --accent: #374151;
  --accent-foreground: #f9fafb;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #374151;
  --input: #374151;
  --ring: #3b82f6;
}
