/* MFA Setup Styles */

/* Shake animation for incorrect verification code */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  20%, 60% { transform: translateX(-5px); }
  40%, 80% { transform: translateX(5px); }
}

.shake-animation {
  animation: shake 0.5s ease-in-out;
}

/* Pulse animation for QR code */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.5);
  }
  70% {
    box-shadow: 0 0 0 15px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

.pulse-animation {
  animation: pulse 2s infinite;
}

/* Digit input focus styles */
.digit-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

/* Recovery code styles */
.recovery-code {
  font-family: monospace;
  letter-spacing: 0.05em;
  transition: all 0.2s ease;
}

.recovery-code:hover {
  background-color: #f0f9ff;
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Dark mode adjustments */
.dark .recovery-code:hover {
  background-color: #1e3a8a;
}

/* Success icon animation */
@keyframes success-icon {
  0% {
    transform: scale(0) rotate(-180deg);
    opacity: 0;
  }
  100% {
    transform: scale(1) rotate(0);
    opacity: 1;
  }
}

.success-icon-animation {
  animation: success-icon 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
}

/* Progress indicator animation */
@keyframes progress {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}

.progress-animation {
  animation: progress 0.5s ease-out forwards;
}
