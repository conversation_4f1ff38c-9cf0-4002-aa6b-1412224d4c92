/* Discord-like styles */

/* Hide scrollbar but keep functionality */
.hide-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.hide-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.hide-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(32, 34, 37, 0.6);
  border-radius: 4px;
}

.hide-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(32, 34, 37, 0.8);
}

/* Typing indicator */
.typing-indicator {
  display: flex;
  align-items: center;
}

.typing-indicator .dot {
  display: inline-block;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: #b9bbbe;
  margin: 0 1px;
  animation: typing-animation 1.4s infinite ease-in-out both;
}

.typing-indicator .dot:nth-child(1) {
  animation-delay: 0s;
}

.typing-indicator .dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator .dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing-animation {
  0%, 80%, 100% {
    transform: scale(0.6);
    opacity: 0.6;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Status indicators */
.status-online {
  background-color: #3ba55c;
}

.status-idle {
  background-color: #faa61a;
}

.status-dnd {
  background-color: #ed4245;
}

.status-offline {
  background-color: #747f8d;
}

/* Server hover effect */
.server-icon {
  transition: border-radius 0.15s ease-out, background-color 0.15s ease-out;
}

.server-icon:hover {
  border-radius: 16px;
}

/* Channel hover effect */
.channel-item {
  transition: background-color 0.1s ease-out;
}

.channel-item:hover {
  background-color: rgba(79, 84, 92, 0.16);
}

/* Message hover effect */
.message-item {
  transition: background-color 0.1s ease-out;
}

.message-item:hover {
  background-color: rgba(79, 84, 92, 0.16);
}

/* Poll animation */
.poll-bar {
  transition: width 0.5s ease-out;
}

/* Reaction animation */
.reaction-button {
  transition: background-color 0.1s ease-out, transform 0.1s ease-out;
}

.reaction-button:hover {
  background-color: rgba(79, 84, 92, 0.32);
  transform: scale(1.1);
}

/* Mention styles */
.mention {
  background-color: rgba(88, 101, 242, 0.3);
  color: #dee0fc;
  border-radius: 3px;
  padding: 0 2px;
}

.mention:hover {
  background-color: rgba(88, 101, 242, 0.4);
  color: #ffffff;
}

/* Code block styles */
.code-block {
  background-color: #2f3136;
  border: 1px solid #202225;
  border-radius: 4px;
  padding: 8px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  color: #dcddde;
  white-space: pre-wrap;
}

/* Attachment styles */
.attachment {
  background-color: #2f3136;
  border-radius: 4px;
  padding: 8px;
  margin-top: 4px;
}

.attachment-image {
  max-width: 100%;
  max-height: 350px;
  border-radius: 4px;
}

/* Pinned message indicator */
.pinned-indicator {
  display: inline-flex;
  align-items: center;
  background-color: rgba(79, 84, 92, 0.48);
  border-radius: 4px;
  padding: 2px 4px;
  font-size: 0.75rem;
  color: #b9bbbe;
}

/* Animation for new messages */
@keyframes new-message {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.new-message {
  animation: new-message 0.2s ease-out;
}
