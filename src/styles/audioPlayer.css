/* Custom audio player styling */
.audio-player {
  -webkit-appearance: none;
  appearance: none;
  background-color: transparent;
  width: 100%;
  margin: 0;
}

/* Webkit (Chrome, Safari) */
.audio-player::-webkit-media-controls-panel {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.audio-player::-webkit-media-controls-play-button {
  background-color: rgba(139, 92, 246, 0.8);
  border-radius: 50%;
  width: 30px;
  height: 30px;
}

.audio-player::-webkit-media-controls-current-time-display,
.audio-player::-webkit-media-controls-time-remaining-display {
  color: #6b7280;
  font-size: 12px;
}

.audio-player::-webkit-media-controls-timeline {
  background-color: rgba(209, 213, 219, 0.5);
  border-radius: 8px;
  height: 4px;
  overflow: hidden;
}

.audio-player::-webkit-media-controls-volume-slider {
  background-color: rgba(209, 213, 219, 0.5);
  border-radius: 8px;
  height: 4px;
}

/* Firefox */
.audio-player::-moz-range-track {
  background-color: rgba(209, 213, 219, 0.5);
  border-radius: 8px;
  height: 4px;
}

.audio-player::-moz-range-thumb {
  background-color: rgba(139, 92, 246, 0.8);
  border: none;
  border-radius: 50%;
  height: 12px;
  width: 12px;
}

/* Dark mode adjustments */
.dark .audio-player::-webkit-media-controls-panel {
  background-color: rgba(31, 41, 55, 0.5);
}

.dark .audio-player::-webkit-media-controls-current-time-display,
.dark .audio-player::-webkit-media-controls-time-remaining-display {
  color: #9ca3af;
}

.dark .audio-player::-webkit-media-controls-timeline,
.dark .audio-player::-webkit-media-controls-volume-slider {
  background-color: rgba(75, 85, 99, 0.5);
}

.dark .audio-player::-moz-range-track {
  background-color: rgba(75, 85, 99, 0.5);
}

/* Animation for recording indicator */
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.animate-pulse {
  animation: pulse 1.5s infinite;
}

/* Typing indicator animation */
.typing-indicator {
  display: inline-flex;
  align-items: center;
}

.typing-indicator .dot {
  display: inline-block;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: currentColor;
  margin: 0 1px;
  animation: typing 1.4s infinite ease-in-out both;
}

.typing-indicator .dot:nth-child(1) {
  animation-delay: 0s;
}

.typing-indicator .dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator .dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.6);
    opacity: 0.6;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Animation for blob backgrounds */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-3000 {
  animation-delay: 3s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}
