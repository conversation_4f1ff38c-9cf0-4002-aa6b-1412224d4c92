/* Global Animations CSS */

/* Background animations */
.app-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  overflow: hidden;
  pointer-events: none;
}

.app-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 20% 30%, rgba(58, 134, 255, 0.15) 0%, transparent 25%),
    radial-gradient(circle at 80% 70%, rgba(255, 0, 110, 0.15) 0%, transparent 25%);
  opacity: var(--pattern-opacity, 0.03);
  z-index: -1;
}

.app-circle {
  position: absolute;
  border-radius: 50%;
  opacity: var(--circle-opacity, 0.15);
  mix-blend-mode: normal;
  pointer-events: none;
}

.app-circle-1 {
  width: 90vw;
  height: 90vw;
  background: var(--gradient-primary, linear-gradient(135deg, #3a86ff 0%, #ff006e 100%));
  top: -45vw;
  right: -45vw;
  filter: blur(80px);
  animation: pulse-slow 15s infinite alternate;
}

.app-circle-2 {
  width: 70vw;
  height: 70vw;
  background: var(--gradient-secondary, linear-gradient(135deg, #8338ec 0%, #3a86ff 100%));
  bottom: -35vw;
  left: -35vw;
  filter: blur(80px);
  animation: pulse-slow 20s infinite alternate-reverse;
}

.app-circle-3 {
  width: 50vw;
  height: 50vw;
  background: var(--gradient-tertiary, linear-gradient(135deg, #ff006e 0%, #ffbe0b 100%));
  top: 40%;
  left: 60%;
  filter: blur(80px);
  animation: pulse-slow 18s infinite alternate;
}

@keyframes pulse-slow {
  0% {
    transform: scale(1);
    opacity: var(--circle-opacity, 0.15);
  }
  50% {
    transform: scale(1.1);
    opacity: calc(var(--circle-opacity, 0.15) * 0.8);
  }
  100% {
    transform: scale(1);
    opacity: var(--circle-opacity, 0.15);
  }
}

/* Logo animations */
.logo-animation {
  filter: drop-shadow(0 0 8px rgba(58, 134, 255, 0.5));
  animation: logo-glow 3s infinite alternate;
  transition: transform 0.3s ease;
}

.logo-animation:hover {
  transform: scale(1.05);
}

@keyframes logo-glow {
  0% {
    filter: drop-shadow(0 0 5px rgba(58, 134, 255, 0.5));
  }
  100% {
    filter: drop-shadow(0 0 15px rgba(58, 134, 255, 0.8));
  }
}

/* Card animations */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Button animations */
.btn-hover {
  position: relative;
  overflow: hidden;
}

.btn-hover::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: translateX(-100%);
  transition: transform 0.8s ease;
}

.btn-hover:hover::before {
  transform: translateX(100%);
}

/* Pulse animation */
.pulse-animation {
  animation: pulse 2s infinite alternate;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.05);
  }
}

/* Blink animation */
.blink-animation {
  animation: blink 2s infinite alternate;
}

@keyframes blink {
  0% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* Wave animation */
.wave-animation {
  position: relative;
  overflow: hidden;
}

.wave-animation::after {
  content: "";
  position: absolute;
  top: 30%;
  left: 0;
  width: 100%;
  height: 40%;
  background: var(--accent-primary, #3a86ff);
  clip-path: polygon(0 50%, 10% 40%, 20% 60%, 30% 30%, 40% 40%, 50% 20%, 60% 40%, 70% 30%, 80% 50%, 90% 40%, 100% 60%, 100% 100%, 0 100%);
  opacity: 0.3;
  animation: wave 5s infinite linear;
}

@keyframes wave {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Float animation */
.float-animation {
  animation: float 6s ease-in-out infinite;
}

.float-animation-slow {
  animation: float 8s ease-in-out infinite;
}

.float-animation-slower {
  animation: float 10s ease-in-out infinite;
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

/* Shimmer animation */
.shimmer {
  position: relative;
  overflow: hidden;
}

.shimmer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to right,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Glass card effect */
.glass-card {
  background: var(--glass-bg, rgba(255, 255, 255, 0.7));
  backdrop-filter: blur(8px);
  border: 1px solid var(--glass-border, rgba(255, 255, 255, 0.5));
  box-shadow: var(--glass-shadow, 0 8px 32px rgba(0, 0, 0, 0.1));
  border-radius: 1rem;
  transition: all 0.3s ease;
}

.glass-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

/* Dark mode specific styles */
.dark .glass-card {
  background: var(--glass-bg, rgba(15, 23, 42, 0.7));
  border: 1px solid var(--glass-border, rgba(30, 41, 59, 0.5));
  box-shadow: var(--glass-shadow, 0 8px 32px rgba(0, 0, 0, 0.2));
}

.dark .logo-animation {
  filter: drop-shadow(0 0 8px rgba(58, 134, 255, 0.3));
}

.dark .logo-animation:hover {
  filter: drop-shadow(0 0 15px rgba(58, 134, 255, 0.5));
}

/* Apply animations to specific elements */
.sidebar-link {
  transition: all 0.3s ease;
}

.sidebar-link:hover {
  transform: translateX(5px);
}

.sidebar-link.active {
  position: relative;
}

.sidebar-link.active::before {
  content: '';
  position: absolute;
  left: -1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 0.25rem;
  height: 1.5rem;
  background: var(--gradient-primary, linear-gradient(135deg, #3a86ff 0%, #ff006e 100%));
  border-radius: 0.125rem;
}

/* Card grid animations */
.card-grid-item {
  transition: all 0.3s ease;
}

.card-grid-item:hover {
  transform: translateY(-5px);
  z-index: 1;
}

/* Staggered animation for lists */
.staggered-item {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.5s forwards;
}

.staggered-item:nth-child(1) { animation-delay: 0.1s; }
.staggered-item:nth-child(2) { animation-delay: 0.2s; }
.staggered-item:nth-child(3) { animation-delay: 0.3s; }
.staggered-item:nth-child(4) { animation-delay: 0.4s; }
.staggered-item:nth-child(5) { animation-delay: 0.5s; }
.staggered-item:nth-child(6) { animation-delay: 0.6s; }
.staggered-item:nth-child(7) { animation-delay: 0.7s; }
.staggered-item:nth-child(8) { animation-delay: 0.8s; }

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .app-circle-1, .app-circle-2, .app-circle-3 {
    opacity: calc(var(--circle-opacity, 0.15) * 0.5);
  }
}
