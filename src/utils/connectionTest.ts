// Connection test utility to verify database connectivity
import { supabase } from '../lib/supabaseClient';

export interface ConnectionTestResult {
  success: boolean;
  error?: string;
  latency?: number;
}

export const testDatabaseConnection = async (): Promise<ConnectionTestResult> => {
  const startTime = Date.now();
  
  try {
    // Simple query to test connection
    const { data, error } = await supabase
      .from('profiles')
      .select('id')
      .limit(1);
    
    const latency = Date.now() - startTime;
    
    if (error) {
      return {
        success: false,
        error: error.message,
        latency
      };
    }
    
    return {
      success: true,
      latency
    };
  } catch (error) {
    const latency = Date.now() - startTime;
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      latency
    };
  }
};

export const testAppPinsTable = async (userId: string): Promise<ConnectionTestResult> => {
  const startTime = Date.now();
  
  try {
    // Test app_pins table specifically
    const { data, error } = await supabase
      .from('app_pins')
      .select('id')
      .eq('user_id', userId)
      .maybeSingle();
    
    const latency = Date.now() - startTime;
    
    if (error) {
      return {
        success: false,
        error: error.message,
        latency
      };
    }
    
    return {
      success: true,
      latency
    };
  } catch (error) {
    const latency = Date.now() - startTime;
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      latency
    };
  }
};

export const retryWithBackoff = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> => {
  let lastError: Error;
  
  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error('Unknown error');
      
      if (attempt === maxRetries - 1) {
        throw lastError;
      }
      
      // Exponential backoff with jitter
      const delay = baseDelay * Math.pow(2, attempt) + Math.random() * 1000;
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError!;
};
