{"name": "care-ai-medical-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:network": "vite --host 0.0.0.0", "dev:debug": "vite --debug", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "preview:production": "vite preview --host 0.0.0.0 --port $PORT", "test": "vitest", "test:coverage": "vitest run --coverage", "supabase": "./run-supabase.sh", "supabase:start": "./run-supabase.sh start", "supabase:stop": "./run-supabase.sh stop", "supabase:init": "./run-supabase.sh init", "supabase:db:push": "./run-supabase.sh db push", "supabase:db:pull": "./run-supabase.sh db pull", "supabase:db:reset": "./run-supabase.sh db reset", "setup:medical-hub": "bash setup-medical-hub.sh", "run:migrations": "bash run-migrations.sh", "run:migrations:remote": "bash run-migrations.sh --remote"}, "dependencies": {"@internationalized/date": "^3.5.2", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toast": "^1.x.x", "@react-three/drei": "^9.88.0", "@react-three/fiber": "^8.18.0", "@supabase/supabase-js": "^2.x.x", "@tensorflow/tfjs": "^4.17.0", "@types/bcryptjs": "^2.4.6", "@types/crypto-js": "^4.2.2", "@types/pako": "^2.0.3", "@types/react-datepicker": "^6.2.0", "@types/three": "^0.175.0", "@types/zxcvbn": "^4.4.5", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "better-sqlite3": "^11.9.1", "chalk": "^5.4.1", "chart.js": "^4.4.9", "chess-timer": "^0.0.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "date-fns": "^2.x.x", "emoji-picker-react": "^4.12.2", "ethers": "^6.11.1", "firebase": "^11.6.0", "firebase-admin": "^13.3.0", "framer-motion": "^12.7.4", "i18next": "^23.10.1", "i18next-browser-languagedetector": "^7.2.2", "i18next-http-backend": "^3.0.2", "leaflet": "^1.9.4", "linkify-plugin-hashtag": "^4.2.0", "linkify-plugin-mention": "^4.2.0", "linkify-react": "^4.2.0", "linkifyjs": "^4.2.0", "lucide-react": "^0.344.0", "pako": "^2.1.0", "qrcode.react": "^4.2.0", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-circular-progressbar": "^2.2.0", "react-datepicker": "^6.9.0", "react-dom": "^18.3.1", "react-dropzone": "^14.2.3", "react-hot-toast": "^2.5.2", "react-i18next": "^14.1.0", "react-leaflet": "^4.2.1", "react-phone-input-2": "^2.15.1", "react-router-dom": "^7.3.0", "react-timeago": "^8.2.0", "react-toastify": "^11.0.5", "recharts": "^2.12.2", "tailwind-merge": "^3.2.0", "three": "^0.175.0", "uuid": "^9.0.1", "zxcvbn": "^4.4.2"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.0.0", "@types/react": "^18.2.64", "@types/react-dom": "^18.2.21", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.18", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.35", "supabase": "^2.22.6", "tailwindcss": "^3.4.1", "typescript": "^5.2.2", "vite": "^5.1.6", "vitest": "^1.0.0"}}