rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // For development - allow all access
    match /{allPaths=**} {
      allow read, write: if true;
    }

    // PRODUCTION RULES - Comment out the above rule and uncomment these for production
    // Default rule - deny all access
    // match /{allPaths=**} {
    //   allow read, write: if false;
    // }

    // Allow read access to all authenticated users for social content
    // match /social/{type}/{userId}/{fileName} {
    //   allow read: if request.auth != null;
    //   allow write: if request.auth != null && request.auth.uid == userId;
    // }

    // Allow read access to all authenticated users for avatars
    // match /avatars/{userId}/{fileName} {
    //   allow read: if request.auth != null;
    //   allow write: if request.auth != null && request.auth.uid == userId;
    // }
  }
}
