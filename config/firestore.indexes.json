{"indexes": [{"collectionGroup": "saved_posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "saved_posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "createdAt", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "DESCENDING"}]}, {"collectionGroup": "posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "hashtags", "arrayConfig": "CONTAINS"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "comments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "postId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "likes", "queryScope": "COLLECTION", "fields": [{"fieldPath": "postId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "social_posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "post_comments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "postId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}]}, {"collectionGroup": "chat_group_messages", "queryScope": "COLLECTION", "fields": [{"fieldPath": "groupId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "chat_messages", "queryScope": "COLLECTION", "fields": [{"fieldPath": "senderId", "order": "ASCENDING"}, {"fieldPath": "receiverId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "chat_messages", "queryScope": "COLLECTION", "fields": [{"fieldPath": "receiverId", "order": "ASCENDING"}, {"fieldPath": "senderId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}], "fieldOverrides": []}